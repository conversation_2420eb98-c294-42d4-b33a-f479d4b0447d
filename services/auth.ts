import { supabase } from '@/lib/supabase'
import { User, UserRole } from '@/types'
import type { AuthError, Session, User as SupabaseUser } from '@supabase/supabase-js'

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  name: string
  role: UserRole
  phone?: string
  location?: string
}

export interface AuthResponse {
  user: User
  token: string
  refreshToken: string
}

export interface ResetPasswordData {
  email: string
}

export interface ChangePasswordData {
  currentPassword: string
  newPassword: string
}

export class AuthService {
  private currentUser: User | null = null
  private session: Session | null = null

  constructor() {
    // Initialize from Supabase session
    this.initializeAuth()
  }

  private async initializeAuth() {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (session) {
        this.session = session
        await this.loadUserProfile(session.user.id)
      }
    } catch (error) {
      console.error('Error initializing auth:', error)
    }
  }

  // Login user
  async login(credentials: LoginCredentials): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      })

      if (error) {
        return { success: false, error: this.handleAuthError(error) }
      }

      if (data.session && data.user) {
        this.session = data.session
        await this.loadUserProfile(data.user.id)
        return { success: true, user: this.currentUser! }
      }

      return { success: false, error: 'Login failed' }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Register new user
  async register(data: RegisterData): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      const { data: authData, error } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            name: data.name,
            role: data.role,
            phone: data.phone,
            location: data.location,
          },
        },
      })

      if (error) {
        return { success: false, error: this.handleAuthError(error) }
      }

      if (authData.session && authData.user) {
        this.session = authData.session

        // Create user profile in database
        const userProfile = await this.createUserProfile(authData.user.id, data)
        if (userProfile) {
          this.currentUser = userProfile
          return { success: true, user: userProfile }
        }
      }

      // If no session but user exists, they need to verify email
      if (authData.user && !authData.session) {
        return {
          success: false,
          error: 'Please check your email and click the verification link to complete registration.'
        }
      }

      return { success: false, error: 'Registration failed' }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Reset password
  async resetPassword(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })

      if (error) {
        return { success: false, error: this.handleAuthError(error) }
      }

      return { success: true }
    } catch (error) {
      console.error('Reset password error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Update password
  async updatePassword(newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        return { success: false, error: this.handleAuthError(error) }
      }

      return { success: true }
    } catch (error) {
      console.error('Update password error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Verify email
  async verifyEmail(token: string, type: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        token_hash: token,
        type: type as any
      })

      if (error) {
        return { success: false, error: this.handleAuthError(error) }
      }

      if (data.session && data.user) {
        this.session = data.session
        await this.loadUserProfile(data.user.id)
        return { success: true }
      }

      return { success: false, error: 'Email verification failed' }
    } catch (error) {
      console.error('Email verification error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Resend verification email
  async resendVerification(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email
      })

      if (error) {
        return { success: false, error: this.handleAuthError(error) }
      }

      return { success: true }
    } catch (error) {
      console.error('Resend verification error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await supabase.auth.signOut()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      this.clearAuth()
    }
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.currentUser
  }

  // Initialize from existing session (for page refreshes/navigation)
  async initializeFromSession(session: any): Promise<void> {
    try {
      this.session = session
      await this.loadUserProfile(session.user.id)
    } catch (error) {
      console.error('Error initializing from session:', error)
      this.clearAuth()
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.currentUser !== null && this.session !== null
  }

  // Get current session
  getSession(): Session | null {
    return this.session
  }

  // Refresh authentication token
  async refreshToken(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.refreshSession()

      if (error) {
        console.error('Token refresh error:', error)
        return false
      }

      if (data.session) {
        this.session = data.session
        return true
      }

      return false
    } catch (error) {
      console.error('Token refresh error:', error)
      return false
    }
  }

  // Reset password
  async resetPassword(data: ResetPasswordData): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })

      if (error) {
        return { success: false, error: this.handleAuthError(error) }
      }

      return { success: true }
    } catch (error) {
      console.error('Reset password error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Change password
  async changePassword(data: ChangePasswordData): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: data.newPassword
      })

      if (error) {
        return { success: false, error: this.handleAuthError(error) }
      }

      return { success: true }
    } catch (error) {
      console.error('Change password error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Update user profile
  async updateProfile(updates: Partial<User>): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      if (!this.currentUser) {
        return { success: false, error: 'No user logged in' }
      }

      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', this.currentUser.id)
        .select()
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      if (data) {
        this.currentUser = { ...this.currentUser, ...data }
        return { success: true, user: this.currentUser }
      }

      return { success: false, error: 'Update failed' }
    } catch (error) {
      console.error('Profile update error:', error)
      return { success: false, error: 'Network error. Please try again.' }
    }
  }

  // Private helper methods
  private async createUserProfile(userId: string, data: RegisterData): Promise<User | null> {
    try {
      const { data: userData, error } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: data.email,
          name: data.name,
          role: data.role,
          phone: data.phone,
          location: data.location,
          status: 'active',
          verified: false,
          preferences: {
            notifications: {
              email: true,
              push: true,
              sms: false,
              marketing: false,
              projectUpdates: true,
              bidAlerts: true,
              messageAlerts: true
            },
            privacy: {
              profileVisibility: 'public',
              showLocation: false,
              showPhone: false,
              allowDirectContact: true
            },
            theme: 'system',
            language: 'en'
          }
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating user profile:', error)
        return null
      }

      return this.mapDatabaseUserToUser(userData)
    } catch (error) {
      console.error('Unexpected error creating user profile:', error)
      return null
    }
  }

  private async loadUserProfile(userId: string): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error loading user profile:', error)
        return
      }

      if (data) {
        this.currentUser = this.mapDatabaseUserToUser(data)
      }
    } catch (error) {
      console.error('Error loading user profile:', error)
    }
  }

  private mapDatabaseUserToUser(data: any): User {
    return {
      id: data.id,
      email: data.email,
      name: data.name,
      role: data.role as UserRole,
      status: data.status,
      avatar: data.avatar_url,
      phone: data.phone,
      location: data.location,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      verified: data.verified,
      preferences: data.preferences || {
        notifications: {
          email: true,
          push: true,
          sms: false,
          marketing: false,
          projectUpdates: true,
          bidAlerts: true,
          messageAlerts: true
        },
        privacy: {
          profileVisibility: 'public',
          showLocation: false,
          showPhone: false,
          allowDirectContact: true
        },
        theme: 'system',
        language: 'en'
      }
    }
  }

  private clearAuth(): void {
    this.currentUser = null
    this.session = null
  }

  private handleAuthError(error: AuthError): string {
    switch (error.message) {
      case 'Invalid login credentials':
        return 'Invalid email or password'
      case 'Email not confirmed':
        return 'Please check your email and click the confirmation link'
      case 'Signup disabled':
        return 'Sign up is currently disabled'
      case 'Invalid email':
        return 'Please enter a valid email address'
      case 'Password should be at least 6 characters':
        return 'Password must be at least 6 characters long'
      case 'User already registered':
        return 'An account with this email already exists'
      default:
        return error.message || 'An unexpected error occurred'
    }
  }

  // Set up auth state listener
  setupAuthListener(callback: (user: User | null) => void): () => void {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          this.session = session
          await this.loadUserProfile(session.user.id)
          callback(this.currentUser)
        } else if (event === 'SIGNED_OUT') {
          this.clearAuth()
          callback(null)
        }
      }
    )

    return () => subscription.unsubscribe()
  }
}

// Create singleton instance
export const authService = new AuthService()
