import { supabase } from '@/lib/supabase'
import { ServiceResponse, createResponse } from './database'

export interface SeedOptions {
  clearExisting?: boolean
  userCount?: number
  contractorCount?: number
  projectCount?: number
  bidCount?: number
  reviewCount?: number
  messageCount?: number
}

export class DatabaseSeeder {
  private defaultOptions: SeedOptions = {
    clearExisting: true,
    userCount: 20,
    contractorCount: 10,
    projectCount: 15,
    bidCount: 30,
    reviewCount: 25,
    messageCount: 50
  }

  async seedDatabase(options: Partial<SeedOptions> = {}): Promise<ServiceResponse<{ message: string }>> {
    try {
      const opts = { ...this.defaultOptions, ...options }
      
      console.log('Starting database seeding...')
      
      if (opts.clearExisting) {
        await this.clearExistingData()
      }

      // Seed in order due to foreign key dependencies
      const users = await this.seedUsers(opts.userCount!)
      const contractors = await this.seedContractors(users.filter(u => u.role === 'pro'), opts.contractorCount!)
      const projects = await this.seedProjects(users.filter(u => u.role === 'customer'), opts.projectCount!)
      const bids = await this.seedBids(projects, contractors, opts.bidCount!)
      const reviews = await this.seedReviews(projects, users, opts.reviewCount!)
      const conversations = await this.seedConversations(projects, users)
      await this.seedMessages(conversations, users, opts.messageCount!)

      console.log('Database seeding completed successfully')
      
      return createResponse({
        message: `Database seeded successfully with ${users.length} users, ${contractors.length} contractors, ${projects.length} projects, ${bids.length} bids, ${reviews.length} reviews`
      })
    } catch (error) {
      console.error('Database seeding failed:', error)
      return createResponse(null, 'Database seeding failed')
    }
  }

  private async clearExistingData(): Promise<void> {
    console.log('Clearing existing test data...')
    
    // Delete in reverse order of dependencies
    await supabase.from('messages').delete().like('conversation_id', 'seed-%')
    await supabase.from('conversations').delete().like('id', 'seed-%')
    await supabase.from('reviews').delete().like('id', 'seed-%')
    await supabase.from('bids').delete().like('id', 'seed-%')
    await supabase.from('projects').delete().like('id', 'seed-%')
    await supabase.from('contractors').delete().like('id', 'seed-%')
    await supabase.from('users').delete().like('id', 'seed-%')
  }

  private async seedUsers(count: number): Promise<any[]> {
    console.log(`Seeding ${count} users...`)
    
    const users = []
    const customerNames = [
      'John Smith', 'Sarah Davis', 'Robert Jones', 'Emily Brown', 'Michael Wilson',
      'Jessica Garcia', 'David Miller', 'Ashley Johnson', 'Christopher Lee', 'Amanda Taylor'
    ]
    const contractorNames = [
      'Jane Wilson', 'Mike Johnson', 'David Martinez', 'Lisa Chen', 'Carlos Rodriguez',
      'Amanda Taylor', 'Steve Thompson', 'Maria Gonzalez', 'Kevin O\'Brien', 'Rachel Kim'
    ]
    
    for (let i = 0; i < count; i++) {
      const isContractor = i >= count * 0.6 // 40% contractors, 60% customers
      const role = isContractor ? 'pro' : 'customer'
      const names = isContractor ? contractorNames : customerNames
      const name = names[i % names.length]
      const email = `${name.toLowerCase().replace(/[^a-z]/g, '')}${i}@example.com`
      
      const user = {
        id: `seed-user-${String(i + 1).padStart(3, '0')}`,
        email,
        name,
        role,
        status: 'active',
        avatar_url: `https://images.unsplash.com/photo-${1472099645785 + i}?w=150`,
        phone: `******-${String(Math.floor(Math.random() * 9000) + 1000)}`,
        location: this.getRandomLocation(),
        verified: Math.random() > 0.2 // 80% verified
      }
      
      users.push(user)
    }

    const { data, error } = await supabase.from('users').insert(users).select()
    if (error) throw error
    
    return data || []
  }

  private async seedContractors(proUsers: any[], count: number): Promise<any[]> {
    console.log(`Seeding ${count} contractors...`)
    
    const contractors = []
    const businessNames = [
      'Elite Kitchen & Bath', 'Premium Home Solutions', 'Bay Area Flooring Co.',
      'Precision Painters', 'Modern Electrical Services', 'Professional Plumbing',
      'Quality Construction', 'Expert Renovations', 'Reliable Contractors', 'Skilled Craftsmen'
    ]
    
    const specialties = [
      ['kitchen', 'bathroom'],
      ['kitchen', 'bathroom', 'flooring'],
      ['flooring'],
      ['painting'],
      ['electrical'],
      ['plumbing'],
      ['kitchen', 'bathroom', 'flooring', 'painting'],
      ['bathroom', 'plumbing'],
      ['kitchen', 'electrical'],
      ['painting', 'flooring']
    ]

    for (let i = 0; i < Math.min(count, proUsers.length); i++) {
      const user = proUsers[i]
      const businessName = businessNames[i % businessNames.length]
      const specialty = specialties[i % specialties.length]
      
      const contractor = {
        id: `seed-contractor-${String(i + 1).padStart(3, '0')}`,
        user_id: user.id,
        business_name: businessName,
        license: `LIC${String(Math.floor(Math.random() * 900000) + 100000)}`,
        specialties: specialty,
        tier: this.getRandomTier(),
        status: 'active',
        rating_average: Number((Math.random() * 1.5 + 3.5).toFixed(1)), // 3.5-5.0
        rating_count: Math.floor(Math.random() * 50) + 5,
        insurance_info: {
          provider: this.getRandomInsuranceProvider(),
          policy: `POL${Math.floor(Math.random() * 900000) + 100000}`,
          liability: [500000, 1000000, 1500000, 2000000][Math.floor(Math.random() * 4)],
          expires: '2024-12-31'
        },
        service_areas: [
          {
            city: user.location.split(', ')[0],
            state: 'CA',
            radius: Math.floor(Math.random() * 20) + 15
          }
        ],
        portfolio: [
          {
            title: `${specialty[0]} Project`,
            description: `Professional ${specialty[0]} work`,
            images: [`/portfolio/${specialty[0]}1.jpg`],
            year: 2023
          }
        ]
      }
      
      contractors.push(contractor)
    }

    const { data, error } = await supabase.from('contractors').insert(contractors).select()
    if (error) throw error
    
    return data || []
  }

  private async seedProjects(customerUsers: any[], count: number): Promise<any[]> {
    console.log(`Seeding ${count} projects...`)
    
    const projects = []
    const projectTemplates = [
      {
        title: 'Modern Kitchen Renovation',
        description: 'Complete kitchen remodel with new cabinets, countertops, and appliances',
        category: 'kitchen',
        requirements: ['Licensed contractor', 'Insurance required', 'Portfolio of kitchen work']
      },
      {
        title: 'Bathroom Remodel',
        description: 'Master bathroom renovation with walk-in shower and double vanity',
        category: 'bathroom',
        requirements: ['Licensed contractor', 'Bathroom renovation experience']
      },
      {
        title: 'Hardwood Flooring Installation',
        description: 'Install hardwood flooring throughout main living areas',
        category: 'flooring',
        requirements: ['Flooring specialist', 'Material recommendations']
      },
      {
        title: 'Interior Painting',
        description: 'Paint interior walls and ceilings with premium paint',
        category: 'painting',
        requirements: ['Professional painter', 'Quality paint brands']
      },
      {
        title: 'Electrical Panel Upgrade',
        description: 'Upgrade electrical panel and add new outlets',
        category: 'electrical',
        requirements: ['Licensed electrician', 'Permit handling']
      }
    ]

    for (let i = 0; i < count; i++) {
      const customer = customerUsers[i % customerUsers.length]
      const template = projectTemplates[i % projectTemplates.length]
      const status = this.getRandomProjectStatus()
      
      const project = {
        id: `seed-project-${String(i + 1).padStart(3, '0')}`,
        title: template.title,
        description: template.description,
        category: template.category,
        status,
        priority: this.getRandomPriority(),
        customer_id: customer.id,
        budget: this.getRandomBudget(template.category),
        timeline: this.getRandomTimeline(),
        location: this.getRandomProjectLocation(),
        requirements: template.requirements,
        photos: [],
        tags: [template.category],
        created_at: this.getRandomDate(-30, 0), // Last 30 days
        updated_at: new Date().toISOString()
      }
      
      if (status === 'completed') {
        project.completed_at = this.getRandomDate(-7, 0) // Completed in last 7 days
      }
      
      projects.push(project)
    }

    const { data, error } = await supabase.from('projects').insert(projects).select()
    if (error) throw error
    
    return data || []
  }

  private getRandomLocation(): string {
    const locations = [
      'San Francisco, CA', 'Oakland, CA', 'Berkeley, CA', 'San Jose, CA',
      'Palo Alto, CA', 'Fremont, CA', 'San Mateo, CA', 'Daly City, CA'
    ]
    return locations[Math.floor(Math.random() * locations.length)]
  }

  private getRandomTier(): 'basic' | 'premium' | 'elite' {
    const rand = Math.random()
    if (rand < 0.2) return 'elite'
    if (rand < 0.5) return 'premium'
    return 'basic'
  }

  private getRandomInsuranceProvider(): string {
    const providers = ['State Farm', 'Allstate', 'Progressive', 'Farmers', 'Liberty Mutual', 'Geico']
    return providers[Math.floor(Math.random() * providers.length)]
  }

  private getRandomProjectStatus(): 'active' | 'in-progress' | 'completed' | 'cancelled' {
    const rand = Math.random()
    if (rand < 0.4) return 'active'
    if (rand < 0.7) return 'in-progress'
    if (rand < 0.9) return 'completed'
    return 'cancelled'
  }

  private getRandomPriority(): 'low' | 'medium' | 'high' {
    const rand = Math.random()
    if (rand < 0.3) return 'low'
    if (rand < 0.7) return 'medium'
    return 'high'
  }

  private getRandomBudget(category: string): any {
    const budgets = {
      kitchen: { min: 10000, max: 30000 },
      bathroom: { min: 5000, max: 20000 },
      flooring: { min: 3000, max: 12000 },
      painting: { min: 2000, max: 8000 },
      electrical: { min: 1500, max: 5000 }
    }
    
    const base = budgets[category] || { min: 2000, max: 10000 }
    return {
      min: base.min,
      max: base.max,
      currency: 'USD',
      flexible: Math.random() > 0.5
    }
  }

  private getRandomTimeline(): any {
    const durations = [7, 14, 21, 30, 45, 60]
    const duration = durations[Math.floor(Math.random() * durations.length)]
    
    return {
      duration,
      flexible: Math.random() > 0.4,
      urgency: ['within-week', 'within-month', 'flexible'][Math.floor(Math.random() * 3)]
    }
  }

  private getRandomProjectLocation(): any {
    const addresses = [
      '123 Main St', '456 Oak Ave', '789 Pine St', '321 Elm Dr', '654 Maple Ln'
    ]
    const cities = [
      { city: 'San Francisco', zipCode: '94102' },
      { city: 'Oakland', zipCode: '94601' },
      { city: 'Berkeley', zipCode: '94704' },
      { city: 'San Jose', zipCode: '95110' },
      { city: 'Palo Alto', zipCode: '94301' }
    ]
    
    const address = addresses[Math.floor(Math.random() * addresses.length)]
    const location = cities[Math.floor(Math.random() * cities.length)]
    
    return {
      address,
      city: location.city,
      state: 'CA',
      zipCode: location.zipCode,
      country: 'USA'
    }
  }

  private getRandomDate(daysAgo: number, daysFromNow: number): string {
    const now = new Date()
    const minDate = new Date(now.getTime() + daysAgo * 24 * 60 * 60 * 1000)
    const maxDate = new Date(now.getTime() + daysFromNow * 24 * 60 * 60 * 1000)
    const randomTime = minDate.getTime() + Math.random() * (maxDate.getTime() - minDate.getTime())
    return new Date(randomTime).toISOString()
  }

  // Additional seeding methods would be implemented here for bids, reviews, conversations, and messages
  // Due to length constraints, these are placeholder implementations
  
  private async seedBids(projects: any[], contractors: any[], count: number): Promise<any[]> {
    // Implementation would create realistic bids for projects
    return []
  }

  private async seedReviews(projects: any[], users: any[], count: number): Promise<any[]> {
    // Implementation would create reviews for completed projects
    return []
  }

  private async seedConversations(projects: any[], users: any[]): Promise<any[]> {
    // Implementation would create conversations between customers and contractors
    return []
  }

  private async seedMessages(conversations: any[], users: any[], count: number): Promise<any[]> {
    // Implementation would create realistic message exchanges
    return []
  }
}

export const databaseSeeder = new DatabaseSeeder()
