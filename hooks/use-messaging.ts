"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import { useUser } from '@/contexts/user-context'
import { useToast } from '@/hooks/use-toast'
import { messagingService } from '@/services/messaging'
import { useRealtimeMessaging } from '@/hooks/use-realtime-messaging'
import type { Tables } from '@/lib/supabase'
import type { MessageData, ConversationData } from '@/services/messaging'

export interface UseMessagingOptions {
  conversationId?: string
  autoFetch?: boolean
}

export interface UseMessagingReturn {
  conversations: any[]
  messages: any[]
  currentConversation: any | null
  loading: boolean
  error: string | null
  sendMessage: (content: string, attachments?: File[]) => Promise<boolean>
  createConversation: (projectId: string, participantIds: string[]) => Promise<string | null>
  selectConversation: (conversationId: string) => void
  markAsRead: (conversationId?: string) => Promise<void>
  uploadAttachment: (file: File) => Promise<string | null>
  refetch: () => Promise<void>
}

export function useMessaging(options: UseMessagingOptions = {}): UseMessagingReturn {
  const { conversationId, autoFetch = true } = options
  const { user } = useUser()
  const { toast } = useToast()

  const [conversations, setConversations] = useState<any[]>([])
  const [messages, setMessages] = useState<any[]>([])
  const [currentConversation, setCurrentConversation] = useState<any | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const unsubscribeRef = useRef<(() => void) | null>(null)

  // Set up real-time messaging
  const { isConnected, sendTypingIndicator } = useRealtimeMessaging({
    conversationId,
    onNewMessage: (message: MessageData) => {
      setMessages(prev => {
        // Avoid duplicates
        if (prev.some(m => m.id === message.id)) return prev
        return [...prev, message].sort((a, b) =>
          new Date(a.sent_at).getTime() - new Date(b.sent_at).getTime()
        )
      })

      // Show notification for new messages
      if (message.sender_id !== user?.id) {
        toast({
          title: "New message",
          description: message.content.length > 50
            ? message.content.substring(0, 50) + "..."
            : message.content
        })
      }
    },
    onConversationUpdate: (conversation: ConversationData) => {
      setConversations(prev => {
        const index = prev.findIndex(c => c.id === conversation.id)
        if (index >= 0) {
          const updated = [...prev]
          updated[index] = { ...updated[index], ...conversation }
          return updated
        }
        return [conversation, ...prev]
      })
    }
  })

  // Fetch conversations for the current user
  const fetchConversations = useCallback(async () => {
    if (!user) return

    setLoading(true)
    setError(null)

    try {
      const response = await messagingService.getUserConversations(user.id)
      if (response.success && response.data) {
        setConversations(response.data)
      } else {
        setError(response.error || 'Failed to fetch conversations')
      }
    } catch (err) {
      setError('An unexpected error occurred while fetching conversations')
    } finally {
      setLoading(false)
    }
  }, [user])

  // Fetch messages for a specific conversation
  const fetchMessages = useCallback(async (convId: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await messagingService.getMessages(convId)
      if (response.success && response.data) {
        setMessages(response.data)
      } else {
        setError(response.error?.message || 'Failed to fetch messages')
      }
    } catch (err) {
      setError('An unexpected error occurred while fetching messages')
    } finally {
      setLoading(false)
    }
  }, [])

  // Send a new message
  const sendMessage = useCallback(async (content: string, attachments?: File[]): Promise<boolean> => {
    if (!user || !currentConversation) {
      toast({
        title: "Error",
        description: "Cannot send message: no active conversation",
        variant: "destructive"
      })
      return false
    }

    try {
      // Upload attachments if any
      const attachmentData = []
      if (attachments && attachments.length > 0) {
        toast({
          title: "Uploading attachments...",
          description: `Uploading ${attachments.length} file(s)`,
        })

        const uploadResults = await messagingService.uploadMessageAttachments(attachments, currentConversation.id)
        if (uploadResults.success && uploadResults.data) {
          attachmentData.push(...uploadResults.data)
        } else {
          toast({
            title: "Upload Error",
            description: uploadResults.error || 'Failed to upload some attachments',
            variant: "destructive"
          })
          // Continue with message even if some uploads failed
        }
      }

      // Determine message type based on content and attachments
      let messageType: 'text' | 'image' | 'file' = 'text'
      if (attachmentData.length > 0) {
        const hasImages = attachmentData.some(att => att.type.startsWith('image/'))
        messageType = hasImages ? 'image' : 'file'
      }

      const messageData = {
        conversation_id: currentConversation.id,
        sender_id: user.id,
        content: content || (attachmentData.length > 0 ? `Sent ${attachmentData.length} attachment(s)` : ''),
        type: messageType,
        attachments: attachmentData
      }

      const response = await messagingService.sendMessage(messageData)
      if (response.success && response.data) {
        // Message will be added via real-time subscription
        toast({
          title: "Message sent",
          description: attachmentData.length > 0 ? `Message with ${attachmentData.length} attachment(s) sent` : undefined,
        })
        return true
      } else {
        toast({
          title: "Error",
          description: response.error || 'Failed to send message',
          variant: "destructive"
        })
        return false
      }
    } catch (err) {
      console.error('Error sending message:', err)
      toast({
        title: "Error",
        description: "An unexpected error occurred while sending message",
        variant: "destructive"
      })
      return false
    }
  }, [user, currentConversation, toast])

  // Create a new conversation
  const createConversation = useCallback(async (projectId: string, participantIds: string[]): Promise<string | null> => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "You must be logged in to create a conversation",
        variant: "destructive"
      })
      return null
    }

    try {
      const conversationData = {
        project_id: projectId,
        participants: [user.id, ...participantIds.filter(id => id !== user.id)]
      }

      const response = await messagingService.createConversation(projectId, conversationData.participants)
      if (response.success && response.data) {
        setConversations(prev => [response.data!, ...prev])
        toast({
          title: "Success",
          description: "Conversation created successfully"
        })
        return response.data.id
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to create conversation",
          variant: "destructive"
        })
        return null
      }
    } catch (err) {
      toast({
        title: "Error",
        description: "An unexpected error occurred while creating conversation",
        variant: "destructive"
      })
      return null
    }
  }, [user, toast])

  // Select a conversation and fetch its messages
  const selectConversation = useCallback(async (convId: string) => {
    const conversation = conversations.find(c => c.id === convId)
    if (conversation) {
      setCurrentConversation(conversation)
      await fetchMessages(convId)
      
      // Set up real-time subscription for this conversation
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }

      unsubscribeRef.current = messagingService.subscribeToConversation(convId, (message) => {
        setMessages(prev => [...prev, message])
      })
    }
  }, [conversations, fetchMessages])

  // Mark messages as read in current conversation
  const markAsRead = useCallback(async (conversationId?: string): Promise<void> => {
    if (!user) return

    const convId = conversationId || currentConversation?.id
    if (!convId) return

    try {
      const response = await messagingService.markMessagesAsRead(convId, user.id)

      if (!response.success) {
        console.error('Failed to mark messages as read:', response.error)
        toast({
          title: "Error",
          description: response.error || "Failed to mark messages as read",
          variant: "destructive"
        })
      }
    } catch (err) {
      console.error('Error marking messages as read:', err)
      toast({
        title: "Error",
        description: "An unexpected error occurred while marking messages as read",
        variant: "destructive"
      })
    }
  }, [user, currentConversation, toast])

  // Upload attachment to storage
  const uploadAttachment = useCallback(async (file: File): Promise<string | null> => {
    if (!user || !currentConversation) return null

    try {
      const result = await messagingService.uploadMessageAttachment(file, currentConversation.id)
      if (result.success && result.data) {
        return result.data.url
      } else {
        toast({
          title: "Upload Error",
          description: result.error || 'Failed to upload attachment',
          variant: "destructive"
        })
        return null
      }
    } catch (error) {
      console.error('Error uploading attachment:', error)
      toast({
        title: "Upload Error",
        description: "An unexpected error occurred during upload",
        variant: "destructive"
      })
      return null
    }
  }, [user, currentConversation, toast])

  // Refetch data
  const refetch = useCallback(async () => {
    await fetchConversations()
    if (currentConversation) {
      await fetchMessages(currentConversation.id)
    }
  }, [fetchConversations, fetchMessages, currentConversation])

  // Initialize
  useEffect(() => {
    if (autoFetch && user) {
      fetchConversations()
    }
  }, [autoFetch, user, fetchConversations])

  // Select initial conversation
  useEffect(() => {
    if (conversationId && conversations.length > 0) {
      selectConversation(conversationId)
    }
  }, [conversationId, conversations, selectConversation])

  // Cleanup subscriptions
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }
    }
  }, [])

  return {
    conversations,
    messages,
    currentConversation,
    loading,
    error,
    sendMessage,
    createConversation,
    selectConversation,
    markAsRead,
    uploadAttachment,
    refetch
  }
}
