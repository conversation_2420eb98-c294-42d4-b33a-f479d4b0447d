#!/usr/bin/env tsx

import { databaseSeeder } from '../services/database-seeder'

async function main() {
  console.log('🌱 Starting database seeding...')
  
  try {
    const result = await databaseSeeder.seedDatabase({
      clearExisting: true,
      userCount: 20,
      contractorCount: 10,
      projectCount: 15,
      bidCount: 30,
      reviewCount: 25,
      messageCount: 50
    })

    if (result.success) {
      console.log('✅ Database seeding completed successfully!')
      console.log(result.data?.message)
    } else {
      console.error('❌ Database seeding failed:', result.error)
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Unexpected error during seeding:', error)
    process.exit(1)
  }
}

// Run the seeding if this script is executed directly
if (require.main === module) {
  main()
}

export { main as seedDatabase }
