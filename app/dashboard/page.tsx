"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { CustomerRoute } from "@/components/route-guard"
import { useUser } from "@/contexts/user-context"
import { useProjects } from "@/hooks/use-projects"
import { EnhancedCard, StatsCard, ProjectCard } from "@/components/ui/enhanced-card"
import { OnboardingTour, customerOnboardingSteps, useOnboarding } from "@/components/ui/onboarding-tour"
import { Plus, TrendingUp, Clock, CheckCircle, MessageCircle, Star, ArrowRight, Calendar, DollarSign, Hammer, HelpCircle } from "lucide-react"
import Link from "next/link"

export default function DashboardPage() {
  const { user } = useUser()
  const { isOnboardingOpen, setIsOnboardingOpen, completeOnboarding, startOnboarding } = useOnboarding()
  const { projects, loading: projectsLoading, error: projectsError } = useProjects({
    autoFetch: true,
    includeCompleted: false
  })

  // Calculate stats from real data
  const activeProjects = projects.filter(p => p.status === 'in-progress' || p.status === 'active')
  const completedProjects = projects.filter(p => p.status === 'completed')

  const stats = [
    { label: "Active Projects", value: activeProjects.length.toString(), icon: Clock, color: "text-blue-600" },
    { label: "Completed Projects", value: completedProjects.length.toString(), icon: CheckCircle, color: "text-green-600" },
    { label: "Total Projects", value: projects.length.toString(), icon: TrendingUp, color: "text-purple-600" },
    { label: "In Bidding", value: projects.filter(p => p.status === 'active').length.toString(), icon: Star, color: "text-yellow-600" },
  ]

  // Recent activity would come from a real activity feed service
  const recentActivity = []

  return (
    <CustomerRoute>
      <div className="min-h-screen bg-white">
        <UnifiedNavigation />

      <div className="container-native section-native">
        {/* Enhanced Header - Mobile-Optimized */}
        <div className="flex flex-col mobile-gap-sm mb-6 sm:mb-8">
          <div className="space-y-2 sm:space-y-1">
            <h1 className="text-base sm:text-lg lg:text-xl font-bold text-slate-900 leading-tight">
              Welcome back{user?.name ? `, ${user.name.split(' ')[0]}` : ''}
            </h1>
            <p className="text-sm sm:text-base text-slate-600 leading-relaxed">
              Here's what's happening with your projects.
            </p>
          </div>

          {/* Mobile: Enhanced compact buttons with better touch targets */}
          <div className="flex sm:hidden gap-3 mt-4">
            <Button
              variant="outline"
              onClick={startOnboarding}
              size="sm"
              className="flex-1 justify-center text-sm py-3 px-4 rounded-xl border-slate-200 hover:border-slate-300 hover:bg-slate-50 transition-all duration-200 touch-target-enhanced"
            >
              <HelpCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              Tour
            </Button>
            <Link href="/" className="flex-1">
              <Button size="sm" className="w-full justify-center text-sm py-3 px-4 rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg shadow-blue-500/25 transition-all duration-200 touch-target-enhanced">
                <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
                New Project
              </Button>
            </Link>
          </div>

          {/* Desktop: Enhanced layout */}
          <div className="hidden sm:flex flex-col sm:flex-row items-stretch sm:items-center mobile-gap-xs mt-4">
            <Button
              variant="outline"
              onClick={startOnboarding}
              size="sm"
              className="btn-native-secondary justify-center"
            >
              <HelpCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              Take Tour
            </Button>
            <Link href="/" className="flex-1 sm:flex-initial">
              <Button size="sm" className="btn-native-primary w-full justify-center">
                <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
                New Project
              </Button>
            </Link>
          </div>
        </div>

        {/* Enhanced Stats Grid - Better mobile presentation */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="card-native-minimal p-4 sm:p-5 hover:shadow-md transition-all duration-200">
              <div className="flex flex-col gap-2">
                <div className="flex items-center justify-between">
                  <p className="text-xs sm:text-sm text-slate-500 font-medium truncate">{stat.label}</p>
                  <stat.icon className={`h-4 w-4 sm:h-4 sm:w-4 ${stat.color} flex-shrink-0`} />
                </div>
                <p className="text-xl sm:text-2xl font-bold text-slate-900 leading-none">{stat.value}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Active Projects - Enhanced Mobile Layout */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-5 sm:mb-6">
              <h2 className="text-base sm:text-lg font-bold text-slate-900">Active Projects</h2>
              <Link href="/projects">
                <Button variant="ghost" size="sm" className="text-slate-500 hover:text-slate-700 text-sm px-3 py-2 rounded-lg">
                  View All
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </div>

            <div className="space-y-3 sm:space-y-4">
              {projectsLoading ? (
                // Enhanced loading skeleton
                Array.from({ length: 2 }).map((_, index) => (
                  <div key={index} className="card-native-minimal p-4 sm:p-6 animate-pulse">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className="w-5 h-5 bg-slate-200 rounded mt-1"></div>
                        <div className="flex-1">
                          <div className="h-5 bg-slate-200 rounded mb-2 w-3/4"></div>
                          <div className="h-4 bg-slate-200 rounded w-1/2"></div>
                        </div>
                      </div>
                      <div className="w-20 h-4 bg-slate-200 rounded"></div>
                    </div>
                  </div>
                ))
              ) : (
                activeProjects.map((project) => (
                  <div key={project.id} className="card-native-minimal p-4 sm:p-6 hover:shadow-lg transition-all duration-200 hover:scale-[1.01]">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4 gap-3 sm:gap-4">
                      <div className="flex items-start space-x-3 flex-1 min-w-0">
                        <div className="flex-shrink-0 mt-0.5">
                          <Hammer className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-base sm:text-lg font-bold text-slate-900 mb-2 leading-tight">{project.title}</h3>
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-slate-500">
                            <span className="flex items-center">
                              <div
                                className={`w-2 h-2 rounded-full mr-2 ${
                                  project.status === "in-progress" ? "bg-blue-500" :
                                  project.status === "active" ? "bg-yellow-500" : "bg-green-500"
                                }`}
                              />
                              {project.status === 'in-progress' ? 'In Progress' :
                               project.status === 'active' ? 'Bidding' :
                               project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                            </span>
                            {project.contractors?.business_name && <span className="truncate">with {project.contractors.business_name}</span>}
                            <span className="text-slate-400">•</span>
                            <span>{project.category}</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-left sm:text-right flex-shrink-0">
                        <div className="text-sm font-semibold text-slate-900">
                          {typeof project.budget === 'object' && project.budget ?
                            (project.budget as any).range || 'Budget set' :
                            'Budget TBD'}
                        </div>
                      </div>
                    </div>

                    {project.status === "in-progress" && project.milestones && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-slate-600">Progress</span>
                          <span className="text-sm font-medium text-slate-900">
                            {Array.isArray(project.milestones) ?
                              `${Math.round((project.milestones.filter((m: any) => m.completed).length / project.milestones.length) * 100)}%` :
                              '0%'}
                          </span>
                        </div>
                        <div className="w-full bg-slate-100 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: Array.isArray(project.milestones) ?
                                `${(project.milestones.filter((m: any) => m.completed).length / project.milestones.length) * 100}%` :
                                '0%'
                            }}
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex items-center space-x-3">
                      {project.status === "active" ? (
                        <Link href={`/project/${project.id}/bids`}>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                            View Bids
                          </Button>
                        </Link>
                      ) : (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            className="bg-transparent border-slate-200/60 hover:border-slate-300"
                          >
                            Message
                          </Button>
                          <Link href={`/project/${project.id}`}>
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                              View Details
                            </Button>
                          </Link>
                        </>
                      )}
                    </div>
                  </div>
                ))
              )}

              {!projectsLoading && activeProjects.length === 0 && (
                <div className="card-native-minimal p-8 sm:p-12 text-center">
                  <Hammer className="h-10 w-10 sm:h-12 sm:w-12 text-slate-300 mx-auto mb-4" />
                  <h3 className="text-base sm:text-lg font-semibold text-slate-900 mb-2">No active projects</h3>
                  <p className="text-sm sm:text-base text-slate-500 mb-6">Start your first renovation project today</p>
                  <Link href="/">
                    <Button className="btn-native-primary">Create Project</Button>
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Sidebar - Mobile-Optimized */}
          <div className="space-y-4 sm:space-y-6">
            {/* Recent Activity - Enhanced Mobile Layout */}
            <div className="card-native-minimal p-4 sm:p-6">
              <h3 className="text-sm sm:text-base font-bold text-slate-900 mb-4 sm:mb-5">Recent Activity</h3>

              <div className="space-y-3 sm:space-y-4">
                {recentActivity.length === 0 ? (
                  <div className="text-center py-6">
                    <Clock className="h-8 w-8 text-slate-300 mx-auto mb-3" />
                    <p className="text-sm text-slate-500">No recent activity</p>
                  </div>
                ) : (
                  recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-0.5">
                        {activity.type === "milestone" ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : activity.type === "bid" ? (
                          <Clock className="h-4 w-4 text-blue-600" />
                        ) : (
                          <MessageCircle className="h-4 w-4 text-purple-600" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-slate-600 leading-relaxed mb-1">{activity.message}</p>
                        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                          <span className="text-xs text-slate-400">{activity.time}</span>
                          <span className="hidden sm:inline text-xs text-slate-400">•</span>
                          <span className="text-xs text-slate-500 truncate">{activity.project}</span>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {recentActivity.length > 0 && (
                <Button variant="ghost" size="sm" className="w-full mt-4 text-slate-500 hover:text-slate-700 text-sm py-2">
                  View All Activity
                </Button>
              )}
            </div>


            {/* Upcoming - Enhanced Mobile Layout */}
            <div className="card-native-minimal p-4 sm:p-6">
              <h3 className="text-sm sm:text-base font-bold text-slate-900 mb-4 sm:mb-5">Upcoming</h3>

              <div className="space-y-3 sm:space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5">
                    <Calendar className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-slate-900 leading-relaxed mb-1">Cabinet Installation</p>
                    <span className="text-xs text-slate-500">Tomorrow, 9:00 AM</span>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5">
                    <MessageCircle className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-slate-900 leading-relaxed mb-1">Project Review Call</p>
                    <span className="text-xs text-slate-500">Dec 18, 2:00 PM</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Onboarding Tour */}
      <OnboardingTour
        steps={customerOnboardingSteps}
        isOpen={isOnboardingOpen}
        onClose={() => setIsOnboardingOpen(false)}
        onComplete={completeOnboarding}
      />

      </div>
    </CustomerRoute>
  )
}
