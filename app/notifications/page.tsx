"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Badge } from "@/components/ui/badge"
import { Bell, MessageCircle, DollarSign, CheckCircle, Clock, Trash2, BookMarkedIcon as MarkAs<PERSON><PERSON>, Hammer, Info } from "lucide-react"

interface Notification {
  id: string
  type: "message" | "payment" | "milestone" | "bid" | "system"
  title: string
  message: string
  time: string
  read: boolean
  project?: string
}

export default function NotificationsPage() {
  const { user } = useUser()
  const [filter, setFilter] = useState("all")
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      type: "milestone",
      title: "Milestone Completed",
      message: "Cabinet installation has been completed for your Kitchen Remodel project",
      time: "2 hours ago",
      read: false,
      project: "Kitchen Remodel",
    },
    {
      id: "2",
      type: "message",
      title: "New Message",
      message: "<PERSON>'s Kitchen Experts sent you a message about material delivery",
      time: "5 hours ago",
      read: false,
      project: "Kitchen Remodel",
    },
    {
      id: "3",
      type: "bid",
      title: "New Bid Received",
      message: "Premium Home Solutions submitted a bid for your Bathroom Renovation",
      time: "1 day ago",
      read: true,
      project: "Bathroom Renovation",
    },
    {
      id: "4",
      type: "payment",
      title: "Payment Released",
      message: "Milestone payment of $3,250 has been released to Mike's Kitchen Experts",
      time: "2 days ago",
      read: true,
      project: "Kitchen Remodel",
    },
    {
      id: "5",
      type: "system",
      title: "Project Update",
      message: "Your Kitchen Remodel project is now 65% complete",
      time: "3 days ago",
      read: true,
      project: "Kitchen Remodel",
    },
  ])

  const filters = [
    { id: "all", label: "All", count: notifications.length },
    { id: "unread", label: "Unread", count: notifications.filter((n) => !n.read).length },
    { id: "message", label: "Messages", count: notifications.filter((n) => n.type === "message").length },
    { id: "milestone", label: "Milestones", count: notifications.filter((n) => n.type === "milestone").length },
    { id: "payment", label: "Payments", count: notifications.filter((n) => n.type === "payment").length },
  ]

  const getIcon = (type: string) => {
    switch (type) {
      case "message":
        return <MessageCircle className="h-5 w-5 text-blue-600" />
      case "payment":
        return <DollarSign className="h-5 w-5 text-green-600" />
      case "milestone":
        return <CheckCircle className="h-5 w-5 text-purple-600" />
      case "bid":
        return <Clock className="h-5 w-5 text-orange-600" />
      default:
        return <Bell className="h-5 w-5 text-slate-600" />
    }
  }

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === "all") return true
    if (filter === "unread") return !notification.read
    return notification.type === filter
  })

  const markAsRead = (id: string) => {
    setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)))
  }

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })))
  }

  const deleteNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id))
  }

  return (
    <div className="min-h-screen bg-white">
      <UnifiedNavigation />

      <div className="container-native section-native">
        {/* Mobile-Native Header */}
        <div className="flex flex-col gap-3 sm:gap-4 mb-5 sm:mb-6">
          <div className="space-y-1">
            <h1 className="text-native-title">Notifications</h1>
            <p className="text-native-subtitle">Stay updated on your projects and messages</p>
          </div>

          {notifications.some((n) => !n.read) && (
            <Button
              onClick={markAllAsRead}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 w-auto justify-center"
            >
              <MarkAsRead className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="hidden sm:inline">Mark All Read</span>
              <span className="sm:hidden">Mark Read</span>
            </Button>
          )}
        </div>

        {/* Mobile-First Minimalistic Filter */}
        <div className="mb-5 sm:mb-6">
          {/* Mobile: Minimalistic horizontal pills */}
          <div className="flex sm:hidden overflow-x-auto scrollbar-hide gap-2 pb-2">
            {filters.map((filterOption) => (
              <button
                key={filterOption.id}
                onClick={() => setFilter(filterOption.id)}
                className={`flex-shrink-0 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-150 min-w-fit ${
                  filter === filterOption.id
                    ? "bg-blue-600 text-white"
                    : "bg-slate-100 text-slate-600 active:bg-slate-200"
                }`}
              >
                {filterOption.label}
                {filterOption.count > 0 && (
                  <span className={`ml-1.5 ${filter === filterOption.id ? "text-blue-100" : "text-slate-500"}`}>
                    {filterOption.count}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Desktop: Card-based filter grid */}
          <div className="hidden sm:grid grid-cols-2 lg:grid-cols-5 gap-3">
            {filters.map((filterOption) => (
              <button
                key={filterOption.id}
                onClick={() => setFilter(filterOption.id)}
                className={`flex items-center justify-between px-4 py-3 rounded-xl text-left transition-all duration-150 ${
                  filter === filterOption.id
                    ? "bg-blue-600 text-white shadow-md"
                    : "bg-slate-100 hover:bg-slate-200 text-slate-700"
                }`}
              >
                <span className="text-sm font-medium truncate">{filterOption.label}</span>
                <span
                  className={`text-xs px-2 py-0.5 rounded-full flex-shrink-0 ${
                    filter === filterOption.id
                      ? "bg-white/20 text-white"
                      : "bg-white text-slate-600"
                  }`}
                >
                  {filterOption.count}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Mobile-Native Notifications List */}
        <div className="space-y-3 sm:space-y-4">
          {filteredNotifications.length === 0 ? (
            <div className="card-native-minimal mobile-spacing-lg text-center">
              <Bell className="h-8 w-8 sm:h-12 sm:w-12 text-slate-300 mx-auto mb-3 sm:mb-4" />
              <h3 className="text-base sm:text-lg font-medium text-slate-900 mb-1 sm:mb-2">No notifications</h3>
              <p className="text-sm sm:text-base text-slate-500">You're all caught up!</p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`card-native-minimal mobile-spacing-md transition-all duration-150 hover:shadow-md ${
                  !notification.read ? "ring-1 ring-blue-200 bg-blue-50/30" : ""
                }`}
              >
                {/* Mobile Layout */}
                <div className="flex sm:hidden flex-col space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-0.5">{getIcon(notification.type)}</div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-1">
                        <h3 className={`text-sm font-semibold leading-tight ${!notification.read ? "text-slate-900" : "text-slate-700"}`}>
                          {notification.title}
                        </h3>
                        <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
                          {!notification.read && <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />}
                          <span className="text-xs text-slate-400">{notification.time}</span>
                        </div>
                      </div>
                      {notification.project && <p className="text-xs text-slate-500 mb-1">{notification.project}</p>}
                      <p className="text-sm text-slate-600 leading-relaxed">{notification.message}</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {!notification.read && (
                        <Button
                          onClick={() => markAsRead(notification.id)}
                          size="sm"
                          className="bg-slate-100 hover:bg-slate-200 text-slate-700 border border-slate-200 text-xs px-3 py-1.5 rounded-md font-medium transition-all duration-200"
                        >
                          Mark Read
                        </Button>
                      )}
                    </div>
                    <Button
                      onClick={() => deleteNotification(notification.id)}
                      size="sm"
                      variant="ghost"
                      className="text-slate-500 hover:text-red-600 hover:bg-red-50 p-2 rounded-md transition-all duration-200"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Desktop Layout */}
                <div className="hidden sm:flex items-start space-x-4">
                  <div className="flex-shrink-0 mt-1">{getIcon(notification.type)}</div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className={`text-lg font-bold ${!notification.read ? "text-slate-900" : "text-slate-700"}`}>
                          {notification.title}
                        </h3>
                        {notification.project && <p className="text-sm text-slate-500">{notification.project}</p>}
                      </div>

                      <div className="flex items-center space-x-2">
                        {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                        <span className="text-sm text-slate-400">{notification.time}</span>
                      </div>
                    </div>

                    <p className="text-slate-600 leading-relaxed mb-4">{notification.message}</p>

                    <div className="flex items-center space-x-3">
                      {!notification.read && (
                        <Button
                          onClick={() => markAsRead(notification.id)}
                          size="sm"
                          className="bg-slate-100 hover:bg-slate-200 text-slate-700 border border-slate-200 px-4 py-2 rounded-lg font-medium transition-all duration-200"
                        >
                          Mark as Read
                        </Button>
                      )}

                      <Button
                        onClick={() => deleteNotification(notification.id)}
                        size="sm"
                        variant="ghost"
                        className="text-slate-500 hover:text-red-600 hover:bg-red-50 p-2 rounded-lg transition-all duration-200"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
