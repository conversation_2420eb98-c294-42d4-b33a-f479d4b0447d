@tailwind base;
@tailwind components;
@tailwind utilities;

/* RenovHub Professional Design System - DriveTime Inspired */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&family=Roboto:wght@300;400;500;600;700;800&family=DIN+Next+Rounded:wght@300;400;500;600;700;800&display=swap');

/* Design Tokens */
:root {
  /* Primary Color Palette - Philippine Green & Cerulean Blue */
  --color-primary: 1 132 69; /* Philippine Green #018445 */
  --color-secondary: 0 171 232; /* Cerulean Blue #00ABE8 */
  --color-bg: 255 255 255; /* White #FFFFFF */
  --color-text: 35 31 32; /* Raisin Black #231F20 */

  /* Extended Color System */
  --color-primary-light: 52 168 83; /* Lighter Philippine Green */
  --color-primary-dark: 1 105 55; /* Darker Philippine Green */
  --color-secondary-light: 96 181 204; /* Lighter Cerulean <PERSON> */
  --color-secondary-dark: 0 137 186; /* Darker Cerulean Blue */

  /* Foundation Colors */
  --color-success: 1 132 69; /* Philippine Green for success */
  --color-warning: 245 158 11; /* Professional amber */
  --color-error: 239 68 68; /* Professional red */
  --color-info: 0 171 232; /* Cerulean Blue for info */

  /* Neutral Palette - Minimalist approach */
  --color-neutral-50: 248 250 252;
  --color-neutral-100: 241 245 249;
  --color-neutral-200: 226 232 240;
  --color-neutral-300: 203 213 225;
  --color-neutral-400: 148 163 184;
  --color-neutral-500: 100 116 139;
  --color-neutral-600: 71 85 105;
  --color-neutral-700: 51 65 85;
  --color-neutral-800: 30 41 59;
  --color-neutral-900: 35 31 32;

  /* Typography Scale - Professional & Accessible */
  --font-family-base: 'Open Sans', 'Roboto', 'DIN Next Rounded', Arial, sans-serif;
  --font-size-base: 16px;
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* Spacing Scale - Generous whitespace */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Premium Border Radius - Sophisticated curves */
  --border-radius: 12px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;

  /* Premium Shadows - Enhanced depth and sophistication */
  --shadow-sm: 0 1px 3px rgba(35, 31, 32, 0.06), 0 1px 2px rgba(35, 31, 32, 0.04);
  --shadow-md: 0 4px 16px rgba(35, 31, 32, 0.08), 0 2px 8px rgba(35, 31, 32, 0.04);
  --shadow-lg: 0 10px 25px rgba(35, 31, 32, 0.1), 0 4px 12px rgba(35, 31, 32, 0.06);
  --shadow-xl: 0 20px 40px rgba(35, 31, 32, 0.12), 0 8px 20px rgba(35, 31, 32, 0.08);
  --shadow-2xl: 0 25px 50px rgba(35, 31, 32, 0.15), 0 12px 25px rgba(35, 31, 32, 0.1);
}

@layer base {
  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variant-ligatures: common-ligatures;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    font-family: var(--font-family-base);
    font-weight: 400;
    line-height: 1.5;
    color: rgb(var(--color-text));
    background-color: rgb(var(--color-bg));
    font-size: var(--font-size-base);
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  /* Professional Typography System */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-base);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    color: rgb(var(--color-text));
    margin-bottom: 0.5em;
  }

  h1 { font-size: 40px; font-weight: 700; } /* H1: 40px */
  h2 { font-size: 28px; font-weight: 700; } /* H2: 28px */
  h3 { font-size: 24px; font-weight: 700; } /* H3: 24px */
  h4 { font-size: var(--text-xl); font-weight: 600; }
  h5 { font-size: var(--text-lg); font-weight: 600; }
  h6 { font-size: var(--text-base); font-weight: 600; }

  p {
    line-height: 1.6;
    color: rgb(var(--color-text));
    font-size: var(--font-size-base);
    font-weight: 400;
    margin-bottom: 1em;
  }

  /* Professional text utilities */
  .text-professional {
    font-family: var(--font-family-base);
    font-weight: 500;
    letter-spacing: -0.01em;
  }

  .text-bold-professional {
    font-family: var(--font-family-base);
    font-weight: 700;
    letter-spacing: -0.02em;
  }

  /* Enhanced Focus States - WCAG AA Compliant */
  *:focus-visible {
    outline: 2px solid rgb(var(--color-primary));
    outline-offset: 2px;
    border-radius: var(--border-radius);
  }
}

/* Professional Component System */
@layer components {
  /* Enhanced Layout Utilities */
  .container-premium {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-mobile-optimized {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-premium {
    @apply py-16 lg:py-24;
  }

  .page-padding {
    @apply pt-8 pb-16 lg:pt-12 lg:pb-24;
  }

  .content-flow {
    @apply space-y-8 lg:space-y-12;
  }

  /* Professional Button System */
  .btn-premium {
    @apply inline-flex items-center justify-center font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
    font-family: var(--font-family-base);
    font-weight: 600;
    letter-spacing: -0.01em;
  }

  .btn-primary {
    @apply btn-premium text-white focus-visible:ring-2 focus-visible:ring-offset-2;
    background: linear-gradient(135deg, rgb(var(--color-primary)) 0%, rgb(var(--color-primary-dark)) 100%);
    color: rgb(var(--color-bg));
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: var(--font-size-base);
    padding: 0.875rem 2rem;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
  }

  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-primary:hover::before {
    left: 100%;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, rgb(var(--color-primary-dark)) 0%, rgb(var(--color-primary)) 100%);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px) scale(1.02);
  }

  .btn-secondary {
    @apply btn-premium focus-visible:ring-2 focus-visible:ring-offset-2;
    background: linear-gradient(135deg, rgb(var(--color-secondary)) 0%, rgb(var(--color-secondary-dark)) 100%);
    color: rgb(var(--color-bg));
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: var(--font-size-base);
    padding: 0.875rem 2rem;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
  }

  .btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-secondary:hover::before {
    left: 100%;
  }

  .btn-secondary:hover {
    background: linear-gradient(135deg, rgb(var(--color-secondary-dark)) 0%, rgb(var(--color-secondary)) 100%);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px) scale(1.02);
  }

  .btn-ghost {
    @apply btn-premium;
    background: transparent;
    color: rgb(var(--color-text));
    border-radius: var(--radius-md);
    font-weight: 600;
    padding: 0.875rem 2rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .btn-ghost::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgb(var(--color-neutral-100));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-md);
  }

  .btn-ghost:hover::before {
    opacity: 1;
  }

  .btn-ghost:hover {
    transform: translateY(-1px);
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--text-sm);
  }

  .btn-lg {
    padding: 1rem 2rem;
    font-size: var(--text-lg);
  }

  /* Premium Card System */
  .card-premium {
    background: rgb(var(--color-bg));
    color: rgb(var(--color-text));
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgb(var(--color-neutral-200));
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .card-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(var(--color-primary), 0.02) 0%, rgba(var(--color-secondary), 0.02) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  .card-premium:hover::before {
    opacity: 1;
  }

  .card-premium:hover {
    box-shadow: var(--shadow-xl);
    border-color: rgb(var(--color-primary) / 0.1);
    transform: translateY(-4px);
  }

  .card-interactive {
    @apply card-premium cursor-pointer;
  }

  .card-interactive:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--shadow-2xl);
  }

  /* Premium Input System */
  .input-premium {
    width: 100%;
    background: rgb(var(--color-bg));
    border: 1.5px solid rgb(var(--color-neutral-300));
    color: rgb(var(--color-text));
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    font-weight: 400;
    padding: 1.125rem 1.5rem;
    border-radius: var(--radius-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    position: relative;
  }

  .input-premium:focus {
    outline: none;
    border-color: rgb(var(--color-primary));
    box-shadow: 0 0 0 4px rgba(var(--color-primary), 0.1), var(--shadow-md);
    transform: translateY(-1px);
  }

  .input-premium:hover:not(:focus) {
    border-color: rgb(var(--color-neutral-400));
    box-shadow: var(--shadow-md);
  }

  .textarea-premium {
    @apply input-premium resize-none;
    min-height: 160px;
  }

  /* Premium Gradient Backgrounds */
  .bg-premium-gradient {
    background: linear-gradient(135deg, rgba(var(--color-primary), 0.05) 0%, rgba(var(--color-secondary), 0.05) 100%);
  }

  .bg-premium-gradient-subtle {
    background: linear-gradient(135deg, rgba(var(--color-bg), 0.95) 0%, rgba(var(--color-neutral-50), 0.95) 100%);
  }

  /* Premium Glass Effect */
  .glass-premium {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Premium Animations */
  .animate-float-gentle {
    animation: float-gentle 6s ease-in-out infinite;
  }

  .animate-glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
  }
}

/* Premium Animations & Effects */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes gradient {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes float-gentle {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-5px) rotate(1deg); }
  66% { transform: translateY(-3px) rotate(-1deg); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.5); }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(var(--color-primary), 0.2);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(var(--color-primary), 0.4);
    transform: scale(1.05);
  }
}

/* Utility Classes */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animation-delay-1000 {
    animation-delay: 1s;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  /* Mobile-First Touch Targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-target-large {
    @apply min-h-[48px] min-w-[48px];
  }

  /* Mobile-Optimized Spacing */
  .mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-margin {
    @apply mx-4 sm:mx-6 lg:mx-8;
  }

  /* Standardized Action Button Positioning */
  .action-button-top-right {
    @apply absolute top-3 right-3 w-8 h-8 p-0 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .action-button-top-left {
    @apply absolute top-3 left-3 w-8 h-8 p-0 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md;
  }

  /* Responsive Text Scaling */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  /* Mobile-First Touch Targets */
  .touch-target-enhanced {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  .touch-target-large {
    @apply min-h-[48px] min-w-[48px] flex items-center justify-center;
  }

  /* Safe Area Support */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .pt-safe-top {
    padding-top: max(12px, env(safe-area-inset-top));
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-First Container */
  .container-mobile {
    @apply px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto;
  }

  .container-mobile-tight {
    @apply px-3 sm:px-4 lg:px-6 max-w-6xl mx-auto;
  }

  /* Mobile-First Spacing */
  .section-mobile {
    @apply py-6 sm:py-8 lg:py-12;
  }

  .section-mobile-tight {
    @apply py-4 sm:py-6 lg:py-8;
  }

  /* Mobile-First Cards */
  .card-mobile {
    @apply bg-white rounded-xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .card-mobile-padding {
    @apply p-4 sm:p-6 lg:p-8;
  }

  /* Mobile-First Buttons */
  .btn-mobile-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-lg shadow-blue-500/25 font-semibold transition-all duration-200 hover:scale-105 touch-target-enhanced text-sm sm:text-base px-4 py-2.5 sm:px-6 sm:py-3;
  }

  .btn-mobile-secondary {
    @apply bg-white border border-slate-200 hover:border-slate-300 text-slate-700 hover:text-slate-900 rounded-xl shadow-sm hover:shadow-md font-medium transition-all duration-200 touch-target-enhanced text-sm sm:text-base px-4 py-2.5 sm:px-6 sm:py-3;
  }

  /* Enhanced Mobile-Native Form Elements */
  .input-mobile {
    @apply w-full px-4 py-3 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base bg-white touch-target-enhanced;
  }

  .textarea-mobile {
    @apply w-full px-4 py-3 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base bg-white resize-none min-h-[120px];
  }

  .input-mobile-enhanced {
    @apply w-full px-4 py-3.5 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base bg-white touch-target-enhanced placeholder:text-slate-400 min-h-[48px];
  }

  .select-mobile {
    @apply w-full px-4 py-3.5 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200 text-base bg-white touch-target-enhanced min-h-[48px] appearance-none;
  }

  /* Mobile-Native Spacing System */
  .mobile-spacing-xs { @apply p-2 sm:p-3; }
  .mobile-spacing-sm { @apply p-3 sm:p-4; }
  .mobile-spacing-md { @apply p-4 sm:p-6; }
  .mobile-spacing-lg { @apply p-6 sm:p-8; }
  .mobile-spacing-xl { @apply p-8 sm:p-12; }

  .mobile-gap-xs { @apply gap-2 sm:gap-3; }
  .mobile-gap-sm { @apply gap-3 sm:gap-4; }
  .mobile-gap-md { @apply gap-4 sm:gap-6; }
  .mobile-gap-lg { @apply gap-6 sm:gap-8; }

  .mobile-margin-xs { @apply m-2 sm:m-3; }
  .mobile-margin-sm { @apply m-3 sm:m-4; }
  .mobile-margin-md { @apply m-4 sm:m-6; }

  /* Mobile-Native Cards */
  .card-native {
    @apply bg-white rounded-2xl border-0 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .card-native-minimal {
    @apply bg-white rounded-xl border border-slate-100 shadow-none hover:shadow-sm transition-all duration-200;
  }

  .card-native-flat {
    @apply bg-slate-50/50 rounded-xl border-0 shadow-none;
  }

  /* Mobile-Native Containers */
  .container-native {
    @apply px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto;
  }

  .container-native-tight {
    @apply px-3 sm:px-4 lg:px-6 max-w-6xl mx-auto;
  }

  .container-native-full {
    @apply px-4 sm:px-6 w-full;
  }

  /* Mobile-Native Sections */
  .section-native {
    @apply py-4 sm:py-6 lg:py-8;
  }

  .section-native-tight {
    @apply py-3 sm:py-4 lg:py-6;
  }

  .section-native-spacious {
    @apply py-6 sm:py-8 lg:py-12;
  }

  /* Mobile-Native Typography - Optimized for small screens */
  .text-native-title {
    @apply text-base sm:text-lg lg:text-xl font-semibold text-slate-900 leading-tight;
  }

  .text-native-subtitle {
    @apply text-sm sm:text-base lg:text-lg text-slate-600 leading-relaxed;
  }

  .text-native-body {
    @apply text-sm sm:text-base text-slate-700 leading-relaxed;
  }

  .text-native-caption {
    @apply text-xs sm:text-sm text-slate-500;
  }

  .text-native-small {
    @apply text-xs text-slate-600;
  }

  /* Mobile-Native Buttons - Optimized sizes */
  .btn-native-primary {
    @apply bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white rounded-xl font-medium transition-all duration-150 touch-target-enhanced text-sm sm:text-base px-4 sm:px-6 py-2.5 sm:py-3 shadow-sm hover:shadow-md active:scale-95;
  }

  .btn-native-secondary {
    @apply bg-slate-100 hover:bg-slate-200 active:bg-slate-300 text-slate-700 rounded-xl font-medium transition-all duration-150 touch-target-enhanced text-sm sm:text-base px-4 sm:px-6 py-2.5 sm:py-3;
  }

  .btn-native-ghost {
    @apply bg-transparent hover:bg-slate-100 active:bg-slate-200 text-slate-600 rounded-xl font-medium transition-all duration-150 touch-target-enhanced text-sm sm:text-base px-3 sm:px-4 py-2;
  }

  .btn-native-compact {
    @apply bg-slate-100 hover:bg-slate-200 active:bg-slate-300 text-slate-700 rounded-lg font-medium transition-all duration-150 text-xs px-3 py-1.5;
  }

  /* Mobile-Native Lists */
  .list-native {
    @apply space-y-1 sm:space-y-2;
  }

  .list-item-native {
    @apply flex items-center p-3 sm:p-4 rounded-xl hover:bg-slate-50 active:bg-slate-100 transition-colors duration-150 touch-target-enhanced;
  }

  /* Mobile-Native Dividers */
  .divider-native {
    @apply border-t border-slate-100 my-4 sm:my-6;
  }

  .divider-native-subtle {
    @apply border-t border-slate-50 my-3 sm:my-4;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  /* Enhanced Mobile Interaction Patterns */
  .mobile-tap-highlight {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .mobile-scroll-smooth {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  .mobile-focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2;
  }

  /* Mobile-Optimized Grid Layouts */
  .grid-mobile-auto {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6;
  }

  .grid-mobile-cards {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }

  .grid-mobile-stats {
    @apply grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4;
  }

  /* Mobile-First Modal and Overlay Styles */
  .modal-mobile {
    @apply fixed inset-0 z-50 bg-white sm:bg-black/50 sm:flex sm:items-center sm:justify-center;
  }

  .modal-content-mobile {
    @apply w-full h-full sm:w-auto sm:h-auto sm:max-w-lg sm:max-h-[90vh] bg-white sm:rounded-2xl sm:shadow-2xl overflow-y-auto;
  }

  /* Enhanced Mobile Typography Scale */
  .text-mobile-xs { @apply text-xs leading-4; }
  .text-mobile-sm { @apply text-sm leading-5; }
  .text-mobile-base { @apply text-base leading-6; }
  .text-mobile-lg { @apply text-lg leading-7; }
  .text-mobile-xl { @apply text-xl leading-8; }

  /* Mobile-Optimized Spacing Utilities */
  .space-mobile-xs > * + * { @apply mt-2; }
  .space-mobile-sm > * + * { @apply mt-3; }
  .space-mobile-md > * + * { @apply mt-4; }
  .space-mobile-lg > * + * { @apply mt-6; }

  /* Premium Shadow System */
  .shadow-premium-sm {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04);
  }

  .shadow-premium-md {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03);
  }

  .shadow-premium-lg {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.08), 0 4px 6px rgba(0, 0, 0, 0.04);
  }

  .shadow-premium-xl {
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 8px 10px rgba(0, 0, 0, 0.04);
  }

  .shadow-premium-2xl {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12), 0 12px 20px rgba(0, 0, 0, 0.06);
  }

  .shadow-premium-3xl {
    box-shadow: 0 35px 60px rgba(0, 0, 0, 0.15), 0 15px 25px rgba(0, 0, 0, 0.08);
  }

  /* Colored Shadows */
  .shadow-blue {
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15), 0 4px 10px rgba(59, 130, 246, 0.08);
  }

  .shadow-emerald {
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.15), 0 4px 10px rgba(16, 185, 129, 0.08);
  }

  .shadow-purple {
    box-shadow: 0 10px 25px rgba(147, 51, 234, 0.15), 0 4px 10px rgba(147, 51, 234, 0.08);
  }

  /* Glass Morphism Effects */
  .glass-effect {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(12px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-effect-dark {
    background: rgba(15, 23, 42, 0.85);
    backdrop-filter: blur(12px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Premium Gradients */
  .gradient-premium-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  .gradient-premium-emerald {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  }

  .gradient-premium-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  }

  .gradient-premium-sunset {
    background: linear-gradient(135deg, #f59e0b 0%, #ef4444 50%, #ec4899 100%);
  }

  .gradient-premium-ocean {
    background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
  }

  /* Accessibility Enhancements */
  .focus-visible-enhanced {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:ring-offset-white;
  }

  .focus-visible-enhanced-dark {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900;
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .text-slate-600 {
      @apply text-slate-800;
    }

    .text-slate-500 {
      @apply text-slate-700;
    }

    .border-slate-200 {
      @apply border-slate-400;
    }
  }

  /* Reduced Motion Support */
  @media (prefers-reduced-motion: reduce) {
    .animate-spin,
    .animate-pulse,
    .animate-bounce,
    .animate-ping,
    .animate-fade-in,
    .animate-slide-up,
    .animate-gradient,
    .animate-float,
    .animate-glow {
      animation: none;
    }

    .transition-all,
    .transition-colors,
    .transition-transform,
    .transition-opacity {
      transition: none;
    }

    .hover\\:scale-105:hover,
    .hover\\:scale-110:hover,
    .group-hover\\:scale-110,
    .group-hover\\:scale-105 {
      transform: none;
    }
  }

  /* Screen Reader Only Content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }



  /* Enhanced Touch Targets for Mobile */
  .touch-target-enhanced {
    @apply min-h-[48px] min-w-[48px] flex items-center justify-center;
  }

  /* Color Blind Friendly Indicators */
  .status-indicator {
    position: relative;
  }

  .status-indicator::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .status-success::before {
    background-color: #10b981;
  }

  .status-warning::before {
    background-color: #f59e0b;
  }

  .status-error::before {
    background-color: #ef4444;
  }

  .status-info::before {
    background-color: #3b82f6;
  }

  .loading-shimmer {
    background: linear-gradient(90deg,
      rgb(var(--color-neutral-100)) 25%,
      rgb(var(--color-neutral-200)) 50%,
      rgb(var(--color-neutral-100)) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .text-balance {
    text-wrap: balance;
  }

  .transition-premium {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Status Colors */
  .status-active {
    @apply bg-blue-50 text-blue-700 border-blue-200;
  }

  .status-completed {
    @apply bg-emerald-50 text-emerald-700 border-emerald-200;
  }

  .status-pending {
    @apply bg-amber-50 text-amber-700 border-amber-200;
  }

  .status-draft {
    @apply bg-slate-50 text-slate-700 border-slate-200;
  }

  /* Custom Scrollbar */
  .scrollbar-premium::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-premium::-webkit-scrollbar-track {
    background: rgb(var(--color-neutral-100));
    border-radius: var(--radius-sm);
  }

  .scrollbar-premium::-webkit-scrollbar-thumb {
    background: rgb(var(--color-neutral-300));
    border-radius: var(--radius-sm);
  }

  .scrollbar-premium::-webkit-scrollbar-thumb:hover {
    background: rgb(var(--color-neutral-400));
  }

  /* Hide Scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Backdrop Effects */
  .backdrop-premium {
    backdrop-filter: blur(12px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.8);
  }

  /* Grid Background Pattern */
  .bg-grid-slate-100 {
    background-image:
      linear-gradient(to right, rgb(241 245 249) 1px, transparent 1px),
      linear-gradient(to bottom, rgb(241 245 249) 1px, transparent 1px);
    background-size: 24px 24px;
  }
}

:root {
  /* Updated Design System Variables */
  --background: hsl(0, 0%, 100%); /* White */
  --foreground: hsl(0, 0%, 14%); /* Raisin Black */
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(0, 0%, 14%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(0, 0%, 14%);
  --primary: hsl(150, 99%, 26%); /* Philippine Green */
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(195, 100%, 45%); /* Cerulean Blue */
  --secondary-foreground: hsl(0, 0%, 100%);
  --muted: hsl(210, 40%, 96%);
  --muted-foreground: hsl(215, 16%, 47%);
  --accent: hsl(195, 100%, 45%); /* Cerulean Blue */
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(214, 32%, 91%);
  --input: hsl(214, 32%, 91%);
  --ring: hsl(150, 99%, 26%);
  --chart-1: hsl(150, 99%, 26%);
  --chart-2: hsl(195, 100%, 45%);
  --chart-3: hsl(43, 96%, 56%);
  --chart-4: hsl(0, 84%, 60%);
  --chart-5: hsl(262, 83%, 58%);
  --radius: 5px; /* Updated to match design guide */
  --sidebar: hsl(0, 0%, 100%);
  --sidebar-foreground: hsl(0, 0%, 14%);
  --sidebar-primary: hsl(150, 99%, 26%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(195, 100%, 45%);
  --sidebar-accent-foreground: hsl(0, 0%, 100%);
  --sidebar-border: hsl(214, 32%, 91%);
  --sidebar-ring: hsl(150, 99%, 26%);

  /* RenovHub Brand Colors - Updated */
  --brand-primary: hsl(150, 99%, 26%); /* Philippine Green */
  --brand-primary-foreground: hsl(0, 0%, 100%);
  --brand-secondary: hsl(195, 100%, 45%); /* Cerulean Blue */
  --brand-secondary-foreground: hsl(0, 0%, 100%);
  --brand-accent: hsl(195, 100%, 45%); /* Cerulean Blue */
  --brand-accent-foreground: hsl(0, 0%, 100%);
  --brand-neutral: hsl(0, 0%, 14%); /* Raisin Black */
  --brand-neutral-foreground: hsl(0, 0%, 100%);

  /* Status Colors */
  --status-success: hsl(150, 99%, 26%); /* Philippine Green */
  --status-warning: hsl(43, 96%, 56%);
  --status-error: hsl(0, 84%, 60%);
  --status-info: hsl(195, 100%, 45%); /* Cerulean Blue */

  /* Surface Colors */
  --surface-elevated: hsl(0, 0%, 100%);
  --surface-subtle: hsl(210, 40%, 98%);
  --surface-muted: hsl(210, 40%, 96%);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);

  /* RenovHub Brand Colors - Dark Mode */
  --brand-primary: oklch(0.60 0.15 220);        /* Lighter Blue for dark mode */
  --brand-primary-foreground: oklch(0.15 0 0);
  --brand-secondary: oklch(0.55 0.12 160);      /* Lighter Green for dark mode */
  --brand-secondary-foreground: oklch(0.15 0 0);
  --brand-accent: oklch(0.70 0.18 35);          /* Lighter Orange for dark mode */
  --brand-accent-foreground: oklch(0.15 0 0);
  --brand-neutral: oklch(0.85 0 0);             /* Light Gray for dark mode */
  --brand-neutral-foreground: oklch(0.15 0 0);

  /* Status Colors - Dark Mode */
  --status-success: oklch(0.65 0.15 140);
  --status-warning: oklch(0.75 0.20 70);
  --status-error: oklch(0.65 0.20 25);
  --status-info: oklch(0.70 0.15 240);

  /* Surface Colors - Dark Mode */
  --surface-elevated: oklch(0.20 0 0);
  --surface-subtle: oklch(0.18 0 0);
  --surface-muted: oklch(0.16 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* RenovHub Brand Colors */
  --color-brand-primary: var(--brand-primary);
  --color-brand-primary-foreground: var(--brand-primary-foreground);
  --color-brand-secondary: var(--brand-secondary);
  --color-brand-secondary-foreground: var(--brand-secondary-foreground);
  --color-brand-accent: var(--brand-accent);
  --color-brand-accent-foreground: var(--brand-accent-foreground);
  --color-brand-neutral: var(--brand-neutral);
  --color-brand-neutral-foreground: var(--brand-neutral-foreground);

  /* Status Colors */
  --color-status-success: var(--status-success);
  --color-status-warning: var(--status-warning);
  --color-status-error: var(--status-error);
  --color-status-info: var(--status-info);

  /* Surface Colors */
  --color-surface-elevated: var(--surface-elevated);
  --color-surface-subtle: var(--surface-subtle);
  --color-surface-muted: var(--surface-muted);

  /* Airbnb Brand Colors */
  --brand-primary: 255 90 95; /* Rausch */
  --brand-primary-foreground: 255 255 255;
  --brand-secondary: 0 132 137; /* Babu */
  --brand-secondary-foreground: 255 255 255;
  --brand-accent: 255 180 0; /* Arches */
  --brand-accent-foreground: 34 34 34;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }
}

@layer components {
  .text-balance {
    text-wrap: balance;
  }

  .animate-in {
    animation: animate-in 0.6s ease-out forwards;
  }

  .fade-in {
    animation: fade-in 0.4s ease-out forwards;
  }

  /* Font Utilities */
  .font-poppins {
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  .font-inter {
    font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  /* Brand Color Utilities */
  .bg-brand-primary {
    background-color: var(--brand-primary);
    color: var(--brand-primary-foreground);
  }

  .bg-brand-secondary {
    background-color: var(--brand-secondary);
    color: var(--brand-secondary-foreground);
  }

  .bg-brand-accent {
    background-color: var(--brand-accent);
    color: var(--brand-accent-foreground);
  }

  .text-brand-primary {
    color: var(--brand-primary);
  }

  .text-brand-secondary {
    color: var(--brand-secondary);
  }

  .text-brand-accent {
    color: var(--brand-accent);
  }

  .border-brand-primary {
    border-color: var(--brand-primary);
  }

  .border-brand-secondary {
    border-color: var(--brand-secondary);
  }

  .border-brand-accent {
    border-color: var(--brand-accent);
  }

  /* Status Color Utilities */
  .bg-status-success {
    background-color: var(--status-success);
    color: white;
  }

  .bg-status-warning {
    background-color: var(--status-warning);
    color: white;
  }

  .bg-status-error {
    background-color: var(--status-error);
    color: white;
  }

  .bg-status-info {
    background-color: var(--status-info);
    color: white;
  }

  .text-status-success {
    color: var(--status-success);
  }

  .text-status-warning {
    color: var(--status-warning);
  }

  .text-status-error {
    color: var(--status-error);
  }

  .text-status-info {
    color: var(--status-info);
  }
}

@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Remove all unnecessary visual noise */
.minimal-input {
  @apply border-0 bg-transparent resize-none outline-none ring-0 focus:ring-0 focus:outline-none;
}

.minimal-button {
  @apply transition-all duration-200 ease-out;
}

.minimal-card {
  @apply bg-white border border-slate-100 transition-all duration-200;
}
