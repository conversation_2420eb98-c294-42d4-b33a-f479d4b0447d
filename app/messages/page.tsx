"use client"

import React, { useState, useEffect } from 'react'
import { UnifiedNavigation } from '@/components/unified-navigation'
import { ConversationList } from '@/components/messaging/conversation-list'
import { MessageThread } from '@/components/messaging/message-thread'
import { MessagingErrorBoundary, ConnectionStatus } from '@/components/messaging/messaging-error-boundary'
import { useUser } from '@/contexts/user-context'
import { useMessaging } from '@/hooks/use-messaging'
import { useRealtimeMessaging } from '@/hooks/use-realtime-messaging'
import { useProjects } from '@/hooks/use-projects'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { MessageCircle, Plus, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function MessagesPage() {
  const { user } = useUser()
  const { toast } = useToast()
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null)
  const [showNewConversationDialog, setShowNewConversationDialog] = useState(false)
  const [selectedProjectId, setSelectedProjectId] = useState<string>('')

  const {
    conversations,
    messages,
    currentConversation,
    loading,
    error,
    createConversation,
    sendMessage,
    markAsRead,
    selectConversation,
    refetch
  } = useMessaging({
    userId: user?.id || '',
    autoFetch: !!user
  })

  // Get user's projects for conversation creation
  const { projects, loading: projectsLoading } = useProjects({
    autoFetch: true,
    includeCompleted: false
  })

  // Get connection status from real-time messaging
  const { isConnected } = useRealtimeMessaging({
    conversationId: selectedConversationId || undefined
  })

  // Handle URL parameters for direct conversation access
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const conversationParam = urlParams.get('conversation')
    if (conversationParam && conversationParam !== selectedConversationId) {
      setSelectedConversationId(conversationParam)
      selectConversation(conversationParam)
    }
  }, [selectedConversationId, selectConversation])

  // Handle conversation selection
  const handleSelectConversation = (conversationId: string) => {
    setSelectedConversationId(conversationId)
    selectConversation(conversationId)
  }

  // Handle creating new conversation
  const handleCreateConversation = async () => {
    if (!selectedProjectId || !user) {
      toast({
        title: "Error",
        description: "Please select a project",
        variant: "destructive"
      })
      return
    }

    try {
      // Find the selected project to get contractor information
      const selectedProject = projects.find(p => p.id === selectedProjectId)
      if (!selectedProject) {
        toast({
          title: "Error",
          description: "Selected project not found",
          variant: "destructive"
        })
        return
      }

      // Determine participants based on user role and project status
      let participants = [user.id]

      if (user.role === 'customer' && selectedProject.contractor_id) {
        // Customer starting conversation with assigned contractor
        participants.push(selectedProject.contractor_id)
      } else if (user.role === 'pro' && selectedProject.customer_id) {
        // Contractor starting conversation with customer
        participants.push(selectedProject.customer_id)
      }

      if (participants.length < 2) {
        toast({
          title: "Error",
          description: "Cannot create conversation: missing participant information",
          variant: "destructive"
        })
        return
      }

      const conversationId = await createConversation(selectedProjectId, participants)
      if (conversationId) {
        setSelectedConversationId(conversationId)
        setShowNewConversationDialog(false)
        setSelectedProjectId('')
        toast({
          title: "Success",
          description: "Conversation created successfully"
        })
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
      toast({
        title: "Error",
        description: "Failed to create conversation",
        variant: "destructive"
      })
    }
  }

  // Handle sending messages
  const handleSendMessage = async (content: string, attachments?: File[]) => {
    if (!currentConversation) return false

    try {
      const success = await sendMessage(content, attachments)
      if (success) {
        // Mark message as read immediately for current user
        await markAsRead(currentConversation.id)
      }
      return success
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      })
      return false
    }
  }

  // Handle marking messages as read
  const handleMarkAsRead = async (_messageId: string) => {
    if (currentConversation) {
      await markAsRead(currentConversation.id)
    }
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-red-300" />
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={refetch} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Show login required state
  if (!user) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-slate-300" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">
              Authentication Required
            </h3>
            <p className="text-slate-500 mb-4">
              Please log in to access your messages
            </p>
            <Button onClick={() => window.location.href = '/login'}>
              Sign In
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <UnifiedNavigation />
      <div className="container-native section-native">
        <div className="flex flex-col gap-3 sm:gap-4 mb-5 sm:mb-6">
          <div className="space-y-1">
            <h1 className="text-native-title">Messages</h1>
            <p className="text-native-subtitle">Communicate with contractors and manage project discussions</p>
          </div>

          {/* Mobile: Single row layout */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <Dialog open={showNewConversationDialog} onOpenChange={setShowNewConversationDialog}>
              <DialogTrigger asChild>
                <Button size="sm" className="btn-native-primary w-full sm:w-auto justify-center">
                  <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
                  New Conversation
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Start New Conversation</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-slate-700 mb-2 block">
                      Select Project
                    </label>
                    <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a project..." />
                      </SelectTrigger>
                      <SelectContent>
                        {projects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            {project.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowNewConversationDialog(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleCreateConversation}
                      disabled={!selectedProjectId || projectsLoading}
                    >
                      {projectsLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : null}
                      Create
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <ConnectionStatus isConnected={isConnected} />
          </div>
        </div>

        <MessagingErrorBoundary>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 h-[calc(100vh-200px)] sm:h-[calc(100vh-180px)]">
            {/* Conversation List - Mobile-Native */}
            <div className="lg:col-span-1 card-native-minimal mobile-spacing-sm flex flex-col h-full">
              <ConversationList
                conversations={conversations}
                selectedConversationId={selectedConversationId}
                onSelectConversation={handleSelectConversation}
                onNewConversation={() => setShowNewConversationDialog(true)}
                loading={loading}
                currentUserId={user?.id || ''}
              />
            </div>

            {/* Message Thread - Mobile-Native */}
            <div className="lg:col-span-2 card-native-minimal flex flex-col h-full overflow-hidden">
              {selectedConversationId && currentConversation ? (
                <MessageThread
                  messages={messages}
                  currentUserId={user?.id || ''}
                  onSendMessage={handleSendMessage}
                  onMarkAsRead={handleMarkAsRead}
                  loading={loading}
                  conversationTitle={currentConversation.projects?.title}
                />
              ) : (
                <div className="flex-1 flex items-center justify-center text-center mobile-spacing-lg">
                  <div>
                    <MessageCircle className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-4 text-slate-300" />
                    <h3 className="text-native-title mb-2">Select a conversation</h3>
                    <p className="text-native-body text-slate-500 mb-4">
                      Choose a conversation from the list to start messaging
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => setShowNewConversationDialog(true)}
                      className="btn-native-secondary"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Start New Conversation
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </MessagingErrorBoundary>
      </div>
    </div>
  )
}
