"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { useUser } from "@/contexts/user-context"
import { ArrowLeft, Star, MapPin, Phone, Mail, MessageCircle, Heart, Shield, Clock, Award } from "lucide-react"
import Link from "next/link"

export default function ContractorProfilePage() {
  const { user } = useUser()
  const [isFavorite, setIsFavorite] = useState(false)
  const [message, setMessage] = useState("")
  const [activeTab, setActiveTab] = useState("overview")

  const contractor = {
    id: "1",
    name: "<PERSON>'s Kitchen Experts",
    specialty: "Kitchen Remodeling",
    rating: 4.9,
    reviewCount: 127,
    location: "San Francisco, CA",
    phone: "(*************",
    email: "<EMAIL>",
    verified: true,
    responseTime: "< 2 hours",
    completedProjects: 89,
    yearsExperience: 15,
    bio: "With over 15 years of experience in kitchen remodeling, <PERSON>'s Kitchen Experts specializes in creating beautiful, functional kitchens that exceed expectations. We pride ourselves on quality craftsmanship, attention to detail, and exceptional customer service.",
    services: ["Kitchen Remodeling", "Cabinet Installation", "Countertop Installation", "Appliance Installation"],
    certifications: ["Licensed Contractor", "Insured", "BBB Accredited"],
    portfolio: [
      {
        id: 1,
        image: "/placeholder.svg?height=300&width=400",
        title: "Modern Kitchen Remodel",
        description: "Complete kitchen transformation with custom cabinets",
      },
      {
        id: 2,
        image: "/placeholder.svg?height=300&width=400",
        title: "Traditional Kitchen",
        description: "Classic design with premium finishes",
      },
      {
        id: 3,
        image: "/placeholder.svg?height=300&width=400",
        title: "Contemporary Kitchen",
        description: "Sleek, minimalist design with high-end appliances",
      },
    ],
    reviews: [
      {
        id: 1,
        author: "Sarah Johnson",
        rating: 5,
        date: "2 weeks ago",
        comment:
          "Mike and his team did an incredible job on our kitchen. The attention to detail was outstanding and they completed the project on time and within budget.",
      },
      {
        id: 2,
        author: "David Chen",
        rating: 5,
        date: "1 month ago",
        comment:
          "Professional, reliable, and skilled. Our new kitchen is exactly what we envisioned. Highly recommend!",
      },
    ],
  }

  const tabs = [
    { id: "overview", label: "Overview" },
    { id: "portfolio", label: "Portfolio" },
    { id: "reviews", label: "Reviews" },
  ]

  const handleSendMessage = () => {
    if (message.trim()) {
      // Handle message sending
      setMessage("")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Back Button */}
        <div className="mb-8">
          <Link href="/contractors">
            <Button variant="ghost" className="p-2">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Contractors
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Enhanced Header */}
            <div className="card-premium p-8">
              <div className="flex flex-col lg:flex-row lg:items-start justify-between mb-8 gap-6">
                <div className="flex items-start space-x-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-2xl font-semibold text-white">{contractor.name.charAt(0)}</span>
                  </div>
                  <div>
                    <div className="flex flex-wrap items-center gap-3 mb-2">
                      <h1 className="text-3xl font-semibold text-slate-900">{contractor.name}</h1>
                      {contractor.verified && (
                        <Shield className="h-6 w-6 text-emerald-500" />
                      )}
                    </div>
                    <p className="text-slate-600 mb-2">{contractor.specialty}</p>
                    <div className="flex items-center space-x-4 text-sm text-slate-500">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-slate-400 mr-1" />
                        {contractor.rating} ({contractor.reviewCount} reviews)
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 text-slate-400 mr-1" />
                        {contractor.location}
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => setIsFavorite(!isFavorite)}
                  className="p-2 hover:bg-slate-50 rounded-lg transition-colors"
                >
                  <Heart className={`h-5 w-5 ${isFavorite ? "text-red-500 fill-current" : "text-slate-400"}`} />
                </button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-slate-50 rounded-lg">
                  <div className="text-2xl font-light text-slate-900">{contractor.completedProjects}</div>
                  <div className="text-sm text-slate-500">Projects</div>
                </div>
                <div className="text-center p-4 bg-slate-50 rounded-lg">
                  <div className="text-2xl font-light text-slate-900">{contractor.yearsExperience}</div>
                  <div className="text-sm text-slate-500">Years Experience</div>
                </div>
                <div className="text-center p-4 bg-slate-50 rounded-lg">
                  <div className="text-2xl font-light text-slate-900">{contractor.responseTime}</div>
                  <div className="text-sm text-slate-500">Response Time</div>
                </div>
              </div>

              {/* Bio */}
              <p className="text-slate-700 leading-relaxed">{contractor.bio}</p>
            </div>

            {/* Tabs */}
            <div className="bg-white border border-slate-100 rounded-xl overflow-hidden">
              <div className="border-b border-slate-100">
                <div className="flex">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`px-6 py-4 text-sm font-medium transition-colors ${
                        activeTab === tab.id
                          ? "text-slate-900 border-b-2 border-slate-900"
                          : "text-slate-500 hover:text-slate-700"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>
              </div>

              <div className="p-6">
                {/* Overview Tab */}
                {activeTab === "overview" && (
                  <div className="space-y-6">
                    <div>
                      <h3 className="font-medium text-slate-900 mb-3">Services</h3>
                      <div className="flex flex-wrap gap-2">
                        {contractor.services.map((service, index) => (
                          <span key={index} className="px-3 py-1 bg-slate-100 text-slate-700 rounded-full text-sm">
                            {service}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-slate-900 mb-3">Certifications</h3>
                      <div className="space-y-2">
                        {contractor.certifications.map((cert, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Award className="h-4 w-4 text-slate-400" />
                            <span className="text-slate-700">{cert}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Portfolio Tab */}
                {activeTab === "portfolio" && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {contractor.portfolio.map((item) => (
                      <div key={item.id} className="group cursor-pointer">
                        <div className="aspect-video bg-slate-100 rounded-lg overflow-hidden mb-3">
                          <img
                            src={item.image || "/placeholder.svg"}
                            alt={item.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <h4 className="font-medium text-slate-900 mb-1">{item.title}</h4>
                        <p className="text-sm text-slate-600">{item.description}</p>
                      </div>
                    ))}
                  </div>
                )}

                {/* Reviews Tab */}
                {activeTab === "reviews" && (
                  <div className="space-y-6">
                    {contractor.reviews.map((review) => (
                      <div key={review.id} className="border-b border-slate-100 pb-6 last:border-b-0">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-slate-900">{review.author}</h4>
                            <div className="flex items-center space-x-2">
                              <div className="flex">
                                {[...Array(review.rating)].map((_, i) => (
                                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                                ))}
                              </div>
                              <span className="text-sm text-slate-500">{review.date}</span>
                            </div>
                          </div>
                        </div>
                        <p className="text-slate-700 leading-relaxed">{review.comment}</p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Card */}
            <div className="bg-white border border-slate-100 rounded-xl p-6">
              <h3 className="font-medium text-slate-900 mb-4">Contact Information</h3>

              <div className="space-y-3 mb-6">
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-700">{contractor.phone}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-700">{contractor.email}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-700">Responds in {contractor.responseTime}</span>
                </div>
              </div>

              <div className="space-y-3">
                <Button className="w-full bg-slate-900 hover:bg-slate-800 text-white">
                  <Phone className="h-4 w-4 mr-2" />
                  Call Now
                </Button>
                <Button variant="outline" className="w-full bg-transparent border-slate-200 hover:border-slate-300">
                  <Mail className="h-4 w-4 mr-2" />
                  Send Email
                </Button>
              </div>
            </div>

            {/* Message Card */}
            <div className="bg-white border border-slate-100 rounded-xl p-6">
              <h3 className="font-medium text-slate-900 mb-4">Send a Message</h3>

              <div className="space-y-4">
                <Textarea
                  placeholder="Tell them about your project..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="border-slate-200 focus:border-slate-300 min-h-[100px]"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  className="w-full bg-slate-900 hover:bg-slate-800 text-white disabled:opacity-50"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Send Message
                </Button>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white border border-slate-100 rounded-xl p-6">
              <h3 className="font-medium text-slate-900 mb-4">Quick Stats</h3>

              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-500">Response Rate:</span>
                  <span className="font-medium text-slate-900">98%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">On-Time Completion:</span>
                  <span className="font-medium text-slate-900">95%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">Repeat Customers:</span>
                  <span className="font-medium text-slate-900">78%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">Average Project:</span>
                  <span className="font-medium text-slate-900">$18K</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
