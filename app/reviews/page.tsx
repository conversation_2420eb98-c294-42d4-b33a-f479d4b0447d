"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { Star, Edit, Trash2, Filter } from "lucide-react"
import Link from "next/link"

interface Review {
  id: string
  projectId: string
  projectName: string
  contractorId: string
  contractorName: string
  rating: number
  comment: string
  date: string
  status: "published" | "draft"
  response?: string
}

export default function ReviewsPage() {
  const [filter, setFilter] = useState("all")
  const [sort, setSort] = useState("newest")
  const [editingReview, setEditingReview] = useState<string | null>(null)
  const [editedComment, setEditedComment] = useState("")

  const reviews: Review[] = [
    {
      id: "rev_1",
      projectId: "1",
      projectName: "Kitchen Remodel",
      contractorId: "1",
      contractorName: "<PERSON>'s Kitchen Experts",
      rating: 5,
      comment:
        "Excellent work! <PERSON> and his team were professional, on time, and delivered exceptional quality. The kitchen looks amazing and was completed on schedule.",
      date: "Nov 30, 2024",
      status: "published",
      response: "Thank you for your kind words! It was a pleasure working with you on your kitchen remodel.",
    },
    {
      id: "rev_2",
      projectId: "3",
      projectName: "Living Room Flooring",
      contractorId: "3",
      contractorName: "Bay Area Flooring Co.",
      rating: 4,
      comment:
        "Good work overall. The flooring looks great, but there were some delays in the schedule. Communication could have been better.",
      date: "Nov 15, 2024",
      status: "published",
    },
    {
      id: "rev_3",
      projectId: "2",
      projectName: "Bathroom Renovation",
      contractorId: "2",
      contractorName: "Premium Home Solutions",
      rating: 3,
      comment:
        "The final result is satisfactory, but there were several issues along the way. The project took longer than expected and there were some unexpected costs.",
      date: "Oct 25, 2024",
      status: "draft",
    },
  ]

  const filteredReviews = reviews
    .filter((review) => {
      if (filter === "all") return true
      if (filter === "published") return review.status === "published"
      if (filter === "draft") return review.status === "draft"
      if (filter === "high") return review.rating >= 4
      if (filter === "low") return review.rating < 4
      return true
    })
    .sort((a, b) => {
      const dateA = new Date(a.date).getTime()
      const dateB = new Date(b.date).getTime()

      if (sort === "newest") return dateB - dateA
      if (sort === "oldest") return dateA - dateB
      if (sort === "highest") return b.rating - a.rating
      if (sort === "lowest") return a.rating - b.rating

      return 0
    })

  const handleEditReview = (review: Review) => {
    setEditingReview(review.id)
    setEditedComment(review.comment)
  }

  const handleSaveReview = () => {
    // Save logic would go here
    setEditingReview(null)
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <Star key={i} className={`h-4 w-4 ${i < rating ? "text-yellow-400 fill-current" : "text-slate-200"}`} />
        ))}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-lg sm:text-xl lg:text-2xl font-light text-slate-900 mb-2">Reviews</h1>
          <p className="text-slate-500">Manage your contractor reviews and ratings</p>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap items-center justify-between gap-4 mb-8">
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filter === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("all")}
              className={filter === "all" ? "bg-slate-900 text-white" : "bg-white border-slate-200"}
            >
              All Reviews
            </Button>
            <Button
              variant={filter === "published" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("published")}
              className={filter === "published" ? "bg-slate-900 text-white" : "bg-white border-slate-200"}
            >
              Published
            </Button>
            <Button
              variant={filter === "draft" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("draft")}
              className={filter === "draft" ? "bg-slate-900 text-white" : "bg-white border-slate-200"}
            >
              Drafts
            </Button>
            <Button
              variant={filter === "high" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("high")}
              className={filter === "high" ? "bg-slate-900 text-white" : "bg-white border-slate-200"}
            >
              High Ratings (4-5)
            </Button>
            <Button
              variant={filter === "low" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("low")}
              className={filter === "low" ? "bg-slate-900 text-white" : "bg-white border-slate-200"}
            >
              Low Ratings (1-3)
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-slate-400" />
            <select
              value={sort}
              onChange={(e) => setSort(e.target.value)}
              className="text-sm border-none bg-transparent focus:outline-none text-slate-900"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="highest">Highest Rated</option>
              <option value="lowest">Lowest Rated</option>
            </select>
          </div>
        </div>

        {/* Reviews List */}
        <div className="space-y-6">
          {filteredReviews.length === 0 ? (
            <Card className="p-12 bg-white border-0 shadow-sm text-center">
              <Star className="h-12 w-12 text-slate-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">No reviews found</h3>
              <p className="text-slate-500 mb-6">You haven't written any reviews yet</p>
              <Link href="/projects">
                <Button className="bg-slate-900 hover:bg-slate-800 text-white">View Your Projects</Button>
              </Link>
            </Card>
          ) : (
            filteredReviews.map((review) => (
              <Card key={review.id} className="p-6 bg-white border-0 shadow-sm">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <Link href={`/contractors/${review.contractorId}`}>
                      <h3 className="font-medium text-slate-900 hover:underline">{review.contractorName}</h3>
                    </Link>
                    <p className="text-sm text-slate-500">
                      <Link href={`/project/${review.projectId}`} className="hover:underline">
                        {review.projectName}
                      </Link>{" "}
                      • {review.date}
                    </p>
                  </div>

                  <div className="flex flex-col items-end">
                    {renderStars(review.rating)}
                    {review.status === "draft" && (
                      <span className="mt-1 text-xs px-2 py-1 rounded-full bg-yellow-50 text-yellow-600">Draft</span>
                    )}
                  </div>
                </div>

                {editingReview === review.id ? (
                  <div className="mb-4">
                    <Textarea
                      value={editedComment}
                      onChange={(e) => setEditedComment(e.target.value)}
                      className="border-slate-200 focus:border-slate-300 min-h-[100px]"
                    />
                    <div className="flex justify-end space-x-2 mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingReview(null)}
                        className="bg-transparent border-slate-200"
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleSaveReview}
                        className="bg-slate-900 hover:bg-slate-800 text-white"
                      >
                        Save Changes
                      </Button>
                    </div>
                  </div>
                ) : (
                  <p className="text-slate-700 leading-relaxed mb-4">{review.comment}</p>
                )}

                {review.response && (
                  <div className="bg-slate-50 p-4 rounded-lg mb-4">
                    <p className="text-sm font-medium text-slate-900 mb-2">Response from {review.contractorName}:</p>
                    <p className="text-sm text-slate-700">{review.response}</p>
                  </div>
                )}

                {editingReview !== review.id && (
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditReview(review)}
                      className="text-slate-500 hover:text-slate-700"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                )}
              </Card>
            ))
          )}
        </div>

        {/* Write Review CTA */}
        <div className="mt-12 bg-slate-900 rounded-xl p-8 text-center">
          <h2 className="text-xl font-medium text-white mb-2">Share Your Experience</h2>
          <p className="text-slate-300 mb-6">
            Your honest reviews help other homeowners find the right contractors for their projects.
          </p>
          <Link href="/projects">
            <Button className="bg-white text-slate-900 hover:bg-slate-100">Write a Review</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
