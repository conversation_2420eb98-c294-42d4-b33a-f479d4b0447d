"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { UnifiedNavigation } from '@/components/unified-navigation'
import { useUser } from '@/contexts/user-context'
import { useMilestones } from '@/hooks/use-milestones'
import { useReviews } from '@/hooks/use-reviews'
import { useMessaging } from '@/hooks/use-messaging'
import { MilestoneTracker } from '@/components/project/milestone-tracker'
import { ReviewCard } from '@/components/reviews/review-card'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import {
  MessageCircle,
  Star,
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  User,
  Phone,
  Mail,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { format } from 'date-fns'

export default function ProjectDetailsPage() {
  const params = useParams()
  const projectId = params?.id as string
  const { user } = useUser()
  const { toast } = useToast()

  const {
    project,
    milestones,
    loading: milestonesLoading,
    updateProjectStatus,
    updateMilestone,
    addMilestone,
    deleteMilestone
  } = useMilestones({ projectId, autoFetch: true })

  const {
    reviews,
    loading: reviewsLoading,
    createReview,
    addResponse,
    markHelpful
  } = useReviews({ projectId, autoFetch: true })

  const {
    conversations,
    createConversation
  } = useMessaging({ autoFetch: true })

  const [activeTab, setActiveTab] = useState('overview')

  // Mock project data - in real app this would come from the project service
  const projectData = {
    id: projectId,
    title: 'Kitchen Remodel',
    description: 'Complete kitchen renovation including new cabinets, countertops, and appliances',
    category: 'kitchen',
    status: 'in-progress',
    customer: {
      id: 'customer-1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      avatar_url: '/placeholder.svg'
    },
    contractor: {
      id: 'contractor-1',
      user_id: 'user-contractor-1', // This is the user ID that should be used for conversations
      business_name: "Mike's Kitchen Experts",
      rating: 4.9,
      users: {
        id: 'user-contractor-1',
        name: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        avatar_url: '/placeholder.svg'
      }
    },
    budget: {
      range: '$15,000 - $25,000',
      approved: '$20,000'
    },
    timeline: {
      start_date: '2024-01-15',
      end_date: '2024-02-15',
      estimated_duration: '4 weeks'
    },
    location: {
      address: '123 Main St, San Francisco, CA 94102'
    },
    created_at: '2024-01-10T10:00:00Z'
  }

  const handleStartConversation = async () => {
    // Enhanced validation and error handling
    if (!user) {
      console.error('User not authenticated')
      toast({
        title: "Authentication Required",
        description: "Please log in to start a conversation",
        variant: "destructive"
      })
      return
    }

    if (!projectData.contractor) {
      console.error('No contractor data available')
      toast({
        title: "Error",
        description: "Contractor information not available",
        variant: "destructive"
      })
      return
    }

    // Use the contractor's user_id for the conversation, not the contractor id
    const contractorUserId = projectData.contractor.user_id || projectData.contractor.users?.id

    if (!contractorUserId) {
      console.error('Contractor user ID not found', projectData.contractor)
      toast({
        title: "Error",
        description: "Unable to identify contractor for messaging",
        variant: "destructive"
      })
      return
    }

    console.log('Starting conversation between:', {
      currentUser: user.id,
      contractorUser: contractorUserId,
      projectId: projectId
    })

    try {
      const conversationId = await createConversation(
        projectId,
        [contractorUserId] // Pass the contractor's user ID, not contractor ID
      )

      if (conversationId) {
        // Navigate to messages page with this conversation
        window.location.href = `/messages?conversation=${conversationId}`
      } else {
        console.error('Failed to create conversation - no conversation ID returned')
        toast({
          title: "Error",
          description: "Failed to create conversation. Please try again.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error in handleStartConversation:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred while starting the conversation",
        variant: "destructive"
      })
    }
  }

  const canEdit = user?.role === 'customer' || user?.role === 'contractor'
  const isCustomer = user?.role === 'customer'
  const isContractor = user?.role === 'contractor'

  if (!projectId) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="flex items-center justify-center h-96">
          <p className="text-slate-500">Project not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Project Header */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-4">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-900">
                  {projectData.title}
                </h1>
                <Badge
                  className={
                    projectData.status === 'completed' ? 'bg-green-100 text-green-800' :
                    projectData.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }
                >
                  {projectData.status.replace('-', ' ').toUpperCase()}
                </Badge>
              </div>

              <p className="text-slate-600 mb-4">{projectData.description}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-600">Budget:</span>
                  <span className="font-medium">{projectData.budget.range}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-600">Duration:</span>
                  <span className="font-medium">{projectData.timeline.estimated_duration}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-600">Start:</span>
                  <span className="font-medium">
                    {format(new Date(projectData.timeline.start_date), 'MMM d, yyyy')}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-600">Location:</span>
                  <span className="font-medium">{projectData.location.address}</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col space-y-3">
              <Button onClick={handleStartConversation} className="bg-blue-600 hover:bg-blue-700">
                <MessageCircle className="h-4 w-4 mr-2" />
                Message {isCustomer ? 'Contractor' : 'Customer'}
              </Button>

              {isCustomer && projectData.status === 'completed' && (
                <Button variant="outline">
                  <Star className="h-4 w-4 mr-2" />
                  Leave Review
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Participants */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Customer Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Customer</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold">
                    {projectData.customer.name.charAt(0)}
                  </span>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-slate-900">{projectData.customer.name}</h3>
                  <div className="space-y-1 text-sm text-slate-600">
                    <div className="flex items-center space-x-2">
                      <Mail className="h-3 w-3" />
                      <span>{projectData.customer.email}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone className="h-3 w-3" />
                      <span>{projectData.customer.phone}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contractor Card */}
          {projectData.contractor && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5" />
                  <span>Contractor</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-semibold">
                      {projectData.contractor.business_name.charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-slate-900">
                      {projectData.contractor.business_name}
                    </h3>
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="flex items-center space-x-1">
                        {Array.from({ length: 5 }, (_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${
                              i < Math.floor(projectData.contractor.rating)
                                ? 'text-yellow-400 fill-current'
                                : 'text-slate-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-slate-600">
                        {projectData.contractor.rating}
                      </span>
                    </div>
                    <div className="space-y-1 text-sm text-slate-600">
                      <div className="flex items-center space-x-2">
                        <Mail className="h-3 w-3" />
                        <span>{projectData.contractor.users.email}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Phone className="h-3 w-3" />
                        <span>{projectData.contractor.users.phone}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="milestones">Milestones</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                {/* Project Details */}
                <Card>
                  <CardHeader>
                    <CardTitle>Project Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium text-slate-900 mb-2">Description</h4>
                        <p className="text-slate-600">{projectData.description}</p>
                      </div>

                      <div>
                        <h4 className="font-medium text-slate-900 mb-2">Category</h4>
                        <Badge variant="outline" className="capitalize">
                          {projectData.category}
                        </Badge>
                      </div>

                      <div>
                        <h4 className="font-medium text-slate-900 mb-2">Timeline</h4>
                        <div className="text-sm text-slate-600">
                          <p>Start: {format(new Date(projectData.timeline.start_date), 'MMMM d, yyyy')}</p>
                          <p>End: {format(new Date(projectData.timeline.end_date), 'MMMM d, yyyy')}</p>
                          <p>Duration: {projectData.timeline.estimated_duration}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                {/* Quick Stats */}
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-slate-600">Progress</span>
                        <span className="font-medium">
                          {project?.progress_percentage || 0}%
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-slate-600">Milestones</span>
                        <span className="font-medium">
                          {milestones.filter(m => m.status === 'completed').length} / {milestones.length}
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-slate-600">Reviews</span>
                        <span className="font-medium">{reviews.length}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="milestones" className="mt-6">
            {project && (
              <MilestoneTracker
                project={project}
                milestones={milestones}
                onUpdateMilestone={updateMilestone}
                onAddMilestone={() => {
                  // This would open a modal to add milestone
                  console.log('Add milestone')
                }}
                onEditMilestone={(milestone) => {
                  // This would open a modal to edit milestone
                  console.log('Edit milestone', milestone)
                }}
                onDeleteMilestone={deleteMilestone}
                onUpdateProjectStatus={updateProjectStatus}
                loading={milestonesLoading}
                canEdit={canEdit}
              />
            )}
          </TabsContent>

          <TabsContent value="reviews" className="mt-6">
            <div className="space-y-6">
              {reviews.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <Star className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                    <p className="text-slate-500">No reviews yet</p>
                    {isCustomer && projectData.status === 'completed' && (
                      <Button className="mt-4">
                        Be the first to leave a review
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ) : (
                reviews.map((review) => (
                  <ReviewCard
                    key={review.id}
                    review={review}
                    onMarkHelpful={markHelpful}
                    onAddResponse={isContractor ? addResponse : undefined}
                    canRespond={isContractor}
                    showProject={false}
                  />
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
