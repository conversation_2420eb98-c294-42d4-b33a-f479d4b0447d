"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Logo } from "@/components/logo"
import { authService } from "@/services/auth"
import { useUser } from "@/contexts/user-context"
import { AlertCircle, Loader2, Eye, EyeOff, User, Briefcase } from "lucide-react"
import Link from "next/link"

export default function RegisterPage() {
  const router = useRouter()
  const { setUser } = useUser()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "customer" as "customer" | "pro",
    phone: "",
    location: ""
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match")
      setLoading(false)
      return
    }

    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters")
      setLoading(false)
      return
    }

    try {
      const result = await authService.register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        role: formData.role,
        phone: formData.phone,
        location: formData.location
      })
      
      if (result.success && result.user) {
        setUser(result.user)
        
        // Redirect based on user role
        if (result.user.role === 'pro') {
          router.push('/pro/onboarding') // Pro users go through onboarding first
        } else {
          router.push('/onboarding') // Customer onboarding
        }
      } else {
        setError(result.error || 'Registration failed')
      }
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Registration error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (error) setError(null)
  }

  return (
    <div className="min-h-screen bg-white flex items-center justify-center safe-area-inset">
      <div className="w-full max-w-md container-mobile-tight">
        {/* Logo - Mobile-First */}
        <div className="text-center mb-8 sm:mb-10 lg:mb-12">
          <Logo size="lg" className="justify-center mb-4 sm:mb-6" />
          <h1 className="text-base sm:text-lg lg:text-xl font-semibold text-slate-900 mb-2">Create your account</h1>
          <p className="text-sm sm:text-base text-slate-600">Join thousands of homeowners and contractors</p>
        </div>

        {/* Role Selection - Mobile-First */}
        <div className="mb-6 sm:mb-8">
          <Label className="text-sm font-medium text-slate-700 mb-3 sm:mb-4 block">I am a:</Label>
          <div className="grid grid-cols-2 gap-2 sm:gap-3">
            <button
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, role: "customer" }))}
              className={`p-3 sm:p-4 rounded-xl border-2 transition-all touch-target-enhanced ${
                formData.role === "customer"
                  ? "border-blue-500 bg-blue-50 text-blue-700"
                  : "border-slate-200 hover:border-slate-300"
              }`}
            >
              <User className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2" />
              <div className="text-sm font-medium">Homeowner</div>
              <div className="text-xs text-slate-500">Need work done</div>
            </button>
            <button
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, role: "pro" }))}
              className={`p-3 sm:p-4 rounded-xl border-2 transition-all touch-target-enhanced ${
                formData.role === "pro"
                  ? "border-emerald-500 bg-emerald-50 text-emerald-700"
                  : "border-slate-200 hover:border-slate-300"
              }`}
            >
              <Briefcase className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-2" />
              <div className="text-sm font-medium">Contractor</div>
              <div className="text-xs text-slate-500">Provide services</div>
            </button>
          </div>
        </div>

        {/* Registration Form - Mobile-First */}
        <EnhancedCard className="card-mobile-padding">
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5">
            {/* Error Message - Mobile-First */}
            {error && (
              <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-xl">
                <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            )}

            {/* Name Field - Mobile-First */}
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter your full name"
                required
                className="input-mobile"
                disabled={loading}
              />
            </div>

            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter your email"
                required
                disabled={loading}
              />
            </div>

            {/* Phone Field */}
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="(*************"
                disabled={loading}
              />
            </div>

            {/* Location Field */}
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                name="location"
                type="text"
                value={formData.location}
                onChange={handleInputChange}
                placeholder="City, State"
                disabled={loading}
              />
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Create a password"
                  required
                  disabled={loading}
                  className="pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {/* Confirm Password Field */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="Confirm your password"
                  required
                  disabled={loading}
                  className="pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                  disabled={loading}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {/* Terms Agreement */}
            <div className="flex items-start space-x-2">
              <input
                id="terms"
                type="checkbox"
                required
                className="mt-1 rounded border-slate-300 text-brand-primary focus:ring-brand-primary"
              />
              <Label htmlFor="terms" className="text-sm text-slate-600 leading-relaxed">
                I agree to the{' '}
                <Link href="/terms" className="text-brand-primary hover:text-brand-primary/80">
                  Terms of Service
                </Link>
                {' '}and{' '}
                <Link href="/privacy" className="text-brand-primary hover:text-brand-primary/80">
                  Privacy Policy
                </Link>
              </Label>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-brand-primary hover:bg-brand-primary/90"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating account...
                </>
              ) : (
                'Create Account'
              )}
            </Button>
          </form>
        </EnhancedCard>

        {/* Sign In Link */}
        <div className="text-center mt-6">
          <p className="text-sm text-slate-500">
            Already have an account?{' '}
            <Link href="/login" className="text-brand-primary hover:text-brand-primary/80 font-medium">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
