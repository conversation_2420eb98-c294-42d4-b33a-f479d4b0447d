"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DollarSign, Clock, CheckCircle, AlertCircle, Download, ArrowRight, Shield, CreditCard } from "lucide-react"
import Link from "next/link"

interface Payment {
  id: string
  projectId: string
  projectName: string
  amount: string
  status: "pending" | "processing" | "completed" | "failed"
  date: string
  contractor: string
  milestone: string
  escrowId?: string
}

export default function PaymentsPage() {
  const [activeTab, setActiveTab] = useState("all")

  const payments: Payment[] = [
    {
      id: "pay_1",
      projectId: "1",
      projectName: "Kitchen Remodel",
      amount: "$3,250.00",
      status: "completed",
      date: "Nov 25, 2024",
      contractor: "<PERSON>'s Kitchen Experts",
      milestone: "Plumbing & Electrical",
      escrowId: "escrow_123456",
    },
    {
      id: "pay_2",
      projectId: "1",
      projectName: "Kitchen Remodel",
      amount: "$4,000.00",
      status: "pending",
      date: "Dec 5, 2024",
      contractor: "<PERSON>'s Kitchen Experts",
      milestone: "Cabinet Installation",
    },
    {
      id: "pay_3",
      projectId: "2",
      projectName: "Bathroom Renovation",
      amount: "$2,500.00",
      status: "processing",
      date: "Dec 10, 2024",
      contractor: "Premium Home Solutions",
      milestone: "Initial Design",
      escrowId: "escrow_789012",
    },
    {
      id: "pay_4",
      projectId: "1",
      projectName: "Kitchen Remodel",
      amount: "$2,000.00",
      status: "completed",
      date: "Nov 20, 2024",
      contractor: "Mike's Kitchen Experts",
      milestone: "Demolition",
      escrowId: "escrow_345678",
    },
    {
      id: "pay_5",
      projectId: "1",
      projectName: "Kitchen Remodel",
      amount: "$1,500.00",
      status: "completed",
      date: "Nov 15, 2024",
      contractor: "Mike's Kitchen Experts",
      milestone: "Design & Planning",
      escrowId: "escrow_901234",
    },
  ]

  const filteredPayments = payments.filter((payment) => {
    if (activeTab === "all") return true
    return payment.status === activeTab
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case "pending":
        return <Clock className="h-5 w-5 text-yellow-600" />
      case "processing":
        return <Clock className="h-5 w-5 text-blue-600" />
      case "failed":
        return <AlertCircle className="h-5 w-5 text-red-600" />
      default:
        return <Clock className="h-5 w-5 text-slate-400" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Paid"
      case "pending":
        return "Awaiting Payment"
      case "processing":
        return "Processing"
      case "failed":
        return "Failed"
      default:
        return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600 bg-green-50"
      case "pending":
        return "text-yellow-600 bg-yellow-50"
      case "processing":
        return "text-blue-600 bg-blue-50"
      case "failed":
        return "text-red-600 bg-red-50"
      default:
        return "text-slate-600 bg-slate-50"
    }
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="container-premium page-padding">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-light text-slate-900 mb-2">Payments</h1>
          <p className="text-slate-500">Manage your project payments and escrow transactions</p>
        </div>

        {/* Payment Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="p-6 bg-white border-0 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-500 mb-1">Total Paid</p>
                <p className="text-2xl font-light text-slate-900">$6,750.00</p>
              </div>
              <div className="p-3 rounded-xl bg-green-50">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6 bg-white border-0 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-500 mb-1">Pending</p>
                <p className="text-2xl font-light text-slate-900">$4,000.00</p>
              </div>
              <div className="p-3 rounded-xl bg-yellow-50">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6 bg-white border-0 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-500 mb-1">In Escrow</p>
                <p className="text-2xl font-light text-slate-900">$2,500.00</p>
              </div>
              <div className="p-3 rounded-xl bg-blue-50">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </Card>
        </div>

        {/* Payment Tabs */}
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="bg-white border border-slate-100 p-1 rounded-lg">
            <TabsTrigger
              value="all"
              className="rounded-md data-[state=active]:bg-slate-900 data-[state=active]:text-white"
            >
              All
            </TabsTrigger>
            <TabsTrigger
              value="pending"
              className="rounded-md data-[state=active]:bg-slate-900 data-[state=active]:text-white"
            >
              Pending
            </TabsTrigger>
            <TabsTrigger
              value="processing"
              className="rounded-md data-[state=active]:bg-slate-900 data-[state=active]:text-white"
            >
              Processing
            </TabsTrigger>
            <TabsTrigger
              value="completed"
              className="rounded-md data-[state=active]:bg-slate-900 data-[state=active]:text-white"
            >
              Completed
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Payment List */}
        <div className="space-y-4">
          {filteredPayments.length === 0 ? (
            <Card className="p-12 bg-white border-0 shadow-sm text-center">
              <DollarSign className="h-12 w-12 text-slate-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">No payments found</h3>
              <p className="text-slate-500 mb-6">There are no payments in this category</p>
            </Card>
          ) : (
            filteredPayments.map((payment) => (
              <Card
                key={payment.id}
                className="p-6 bg-white border-0 shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="mt-1">{getStatusIcon(payment.status)}</div>
                    <div>
                      <h3 className="font-medium text-slate-900 mb-1">
                        {payment.milestone} - {payment.projectName}
                      </h3>
                      <p className="text-sm text-slate-500 mb-2">
                        To: {payment.contractor} • {payment.date}
                      </p>
                      {payment.escrowId && <p className="text-xs text-slate-400">Escrow ID: {payment.escrowId}</p>}
                    </div>
                  </div>

                  <div className="text-right">
                    <p className="text-lg font-light text-slate-900 mb-1">{payment.amount}</p>
                    <div className={`inline-block px-2 py-1 rounded-full text-xs ${getStatusColor(payment.status)}`}>
                      {getStatusText(payment.status)}
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-slate-100 flex items-center justify-between">
                  <Link href={`/project/${payment.projectId}`}>
                    <Button variant="ghost" size="sm" className="text-slate-500 hover:text-slate-700">
                      View Project
                    </Button>
                  </Link>

                  <div className="flex space-x-2">
                    {payment.status === "pending" && (
                      <Button size="sm" className="bg-slate-900 hover:bg-slate-800 text-white">
                        <DollarSign className="h-4 w-4 mr-2" />
                        Pay Now
                      </Button>
                    )}

                    {payment.status === "completed" && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-transparent border-slate-200 hover:border-slate-300"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Receipt
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        {/* Add Payment Method */}
        <div className="mt-12">
          <Card className="p-6 bg-white border-0 shadow-sm">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-slate-900">Payment Methods</h2>
              <Button variant="outline" className="bg-transparent border-slate-200 hover:border-slate-300">
                <CreditCard className="h-4 w-4 mr-2" />
                Add Payment Method
              </Button>
            </div>

            <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-slate-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="h-5 w-5 text-slate-600" />
                </div>
                <div>
                  <p className="font-medium text-slate-900">•••• •••• •••• 4242</p>
                  <p className="text-sm text-slate-500">Expires 12/25</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-xs px-2 py-1 rounded-full bg-green-50 text-green-600">Default</span>
                <Button variant="ghost" size="sm" className="text-slate-400 hover:text-slate-600">
                  Edit
                </Button>
              </div>
            </div>
          </Card>
        </div>

        {/* Escrow Information */}
        <div className="mt-8 bg-blue-50 border border-blue-100 rounded-xl p-6">
          <div className="flex items-start space-x-4">
            <div className="p-3 rounded-xl bg-blue-100">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-slate-900 mb-2">Payment Protection with Escrow</h3>
              <p className="text-slate-700 mb-4">
                RenovHub uses secure escrow payments to protect both homeowners and contractors. Funds are only released
                when milestones are completed and verified.
              </p>
              <Link href="/help">
                <Button variant="outline" size="sm" className="bg-transparent border-blue-200 text-blue-700">
                  Learn More
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
