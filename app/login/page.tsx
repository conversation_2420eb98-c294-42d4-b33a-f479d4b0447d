"use client"

import { useState, Suspense, useEffect } from "react"
import * as React from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Logo } from "@/components/logo"
import { authService } from "@/services/auth"
import { useUser } from "@/contexts/user-context"
import { AlertCircle, Loader2, Eye, EyeOff } from "lucide-react"
import Link from "next/link"

function LoginForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setUser, user, isAuthenticated } = useUser()
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)

  // Handle successful authentication
  useEffect(() => {
    if (isAuthenticated && user && loading) {
      // Authentication successful, redirect
      const returnUrl = searchParams?.get('returnUrl') || searchParams?.get('redirect')
      if (returnUrl) {
        router.push(decodeURIComponent(returnUrl))
      } else {
        // Redirect based on user role
        if (user.role === 'pro') {
          router.push('/pro/dashboard')
        } else {
          router.push('/dashboard')
        }
      }
    }
  }, [isAuthenticated, user, loading, router, searchParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Prevent double submission
    if (loading) return

    setLoading(true)
    setError(null)

    try {
      const result = await authService.login(formData)

      if (result.success && result.user) {
        setUser(result.user)
        // Redirect will be handled by useEffect when user state updates
      } else {
        setError(result.error || 'Login failed')
        setLoading(false) // Reset loading state on error
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.')
      console.error('Login error:', err)
      setLoading(false) // Reset loading state on error
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (error) setError(null) // Clear error when user starts typing
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 flex items-center justify-center safe-area-inset">
      <div className="w-full max-w-md mx-auto px-4 sm:px-6">
        {/* Enhanced Header - Better Desktop Presentation */}
        <div className="text-center mb-8 sm:mb-10">
          <Logo size="xl" className="justify-center mb-4 sm:mb-6" />
          <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-900 mb-2">Welcome back</h1>
          <p className="text-base sm:text-lg text-slate-600">Sign in to your account</p>
        </div>

        {/* Enhanced Login Form - Better Desktop Presentation */}
        <div className="bg-white rounded-2xl shadow-xl border border-slate-200/60 p-6 sm:p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Enhanced Error Message */}
            {error && (
              <div className="flex items-center space-x-3 p-4 bg-red-50 border border-red-200 rounded-xl">
                <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
                <span className="text-sm font-medium text-red-700">{error}</span>
              </div>
            )}

            {/* Enhanced Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-semibold text-slate-700">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter your email address"
                required
                disabled={loading}
                className="h-12 text-base border-slate-300 focus:border-blue-500 focus:ring-blue-500/20"
                autoComplete="email"
              />
            </div>

            {/* Enhanced Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-semibold text-slate-700">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Enter your password"
                  required
                  disabled={loading}
                  className="h-12 text-base pr-12 border-slate-300 focus:border-blue-500 focus:ring-blue-500/20"
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 p-2 rounded-lg hover:bg-slate-100 transition-colors"
                  disabled={loading}
                  tabIndex={-1}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {/* Enhanced Form Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <input
                  id="remember"
                  type="checkbox"
                  className="rounded border-slate-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                  disabled={loading}
                />
                <Label htmlFor="remember" className="text-sm text-slate-600 cursor-pointer">
                  Remember me
                </Label>
              </div>
              <Link
                href="/forgot-password"
                className="text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors"
                tabIndex={loading ? -1 : 0}
              >
                Forgot password?
              </Link>
            </div>

            {/* Enhanced Submit Button - Fixed Loading State */}
            <Button
              type="submit"
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading || !formData.email || !formData.password}
            >
              {loading ? (
                <>
                  <Loader2 className="h-5 w-5 mr-2 animate-spin flex-shrink-0" />
                  Signing in...
                </>
              ) : (
                'Sign in'
              )}
            </Button>
          </form>
        </div>

        {/* Enhanced Sign Up Link */}
        <div className="text-center mt-6 sm:mt-8">
          <p className="text-base text-slate-600">
            Don't have an account?{' '}
            <Link href="/register" className="text-blue-600 hover:text-blue-700 font-semibold transition-colors">
              Sign up
            </Link>
          </p>
        </div>

        {/* Demo Access - Subtle Link for Testing */}
        {process.env.NODE_ENV === 'development' && (
          <div className="text-center mt-4">
            <details className="inline-block">
              <summary className="text-xs text-slate-400 hover:text-slate-600 cursor-pointer">
                Demo Access
              </summary>
              <div className="mt-2 space-y-1">
                <button
                  type="button"
                  onClick={() => setFormData({ email: '<EMAIL>', password: 'demo123' })}
                  className="block w-full text-xs text-slate-500 hover:text-slate-700 py-1"
                >
                  Customer Demo
                </button>
                <button
                  type="button"
                  onClick={() => setFormData({ email: '<EMAIL>', password: 'demo123' })}
                  className="block w-full text-xs text-slate-500 hover:text-slate-700 py-1"
                >
                  Pro Demo
                </button>
              </div>
            </details>
          </div>
        )}

        {/* Enhanced Footer */}
        <div className="text-center mt-6 sm:mt-8 px-4">
          <p className="text-sm text-slate-500 leading-relaxed">
            By signing in, you agree to our{' '}
            <Link href="/terms" className="text-slate-600 hover:text-slate-800 underline transition-colors">
              Terms of Service
            </Link>
            {' '}and{' '}
            <Link href="/privacy" className="text-slate-600 hover:text-slate-800 underline transition-colors">
              Privacy Policy
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-sm text-slate-600">Loading...</p>
        </div>
      </div>
    }>
      <LoginForm />
    </Suspense>
  )
}
